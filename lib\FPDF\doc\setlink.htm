<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetLink</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetLink</h1>
<code>SetLink(<b>int</b> link [, <b>float</b> y [, <b>int</b> page]])</code>
<h2>Description</h2>
Indique la page et la position vers lesquelles pointe un lien interne.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>link</code></dt>
<dd>
Identifiant du lien retourné par AddLink().
</dd>
<dt><code>y</code></dt>
<dd>
Ordonnée de la position; <code>-1</code> indique la position courante.
La valeur par défaut est <code>0</code> (haut de la page).
</dd>
<dt><code>page</code></dt>
<dd>
Numéro de la page; <code>-1</code> indique la page courante. C'est la valeur par défaut.
</dd>
</dl>
<h2>Voir</h2>
<a href="addlink.htm">AddLink</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
