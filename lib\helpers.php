<?php
/**
 * Helper functions for CSV operations.
 */

// Read data from a CSV file
function readCsv($file) {
    $rows = [];
    if (($handle = fopen($file, 'r')) !== false) {
        while (($data = fgetcsv($handle)) !== false) {
            $rows[] = $data;
        }
        fclose($handle);
    }
    return $rows;
}

// Write a single row to a CSV file
function writeCsv($file, $data) {
    $handle = fopen($file, 'a');
    fputcsv($handle, $data);
    fclose($handle);
}

// Overwrite a CSV file with new data
function overwriteCsv($file, $data) {
    $handle = fopen($file, 'w');
    foreach ($data as $row) {
        fputcsv($handle, $row);
    }
    fclose($handle);
}

// Delete a row from a CSV file by index
function deleteCsvRow($file, $index) {
    $rows = readCsv($file);
    if (isset($rows[$index])) {
        unset($rows[$index]);
        overwriteCsv($file, $rows);
    }
}
?>
