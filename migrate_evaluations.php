<?php
require_once __DIR__ . '/config/database.php';

// Fonction pour nettoyer les données
function cleanData($value) {
    return trim(str_replace('"', '', $value));
}

try {
    // Vider la table avant la migration
    $db->exec('TRUNCATE TABLE evaluations');
    
    // Lire le fichier CSV
    $filename = __DIR__ . '/evaluations.csv';
    if (file_exists($filename) && ($handle = fopen($filename, 'r')) !== false) {
        // Vider la table avant la migration
        $db->exec('TRUNCATE TABLE evaluations');
        
        // Sauter la première ligne (en-têtes)
        fgetcsv($handle, 1000, ',');
        
        // Préparer la requête d'insertion
        $stmt = $db->prepare('INSERT INTO evaluations (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)');
        
        // Lire et insérer chaque ligne
        while (($data = fgetcsv($handle, 1000, ',')) !== false) {
            if (count($data) >= 6) {
                try {
                    $values = [
                        cleanData($data[0]), // prestataire
                        cleanData($data[1]), // service
                        intval(cleanData($data[2])), // qualite
                        intval(cleanData($data[3])), // delai
                        intval(cleanData($data[4])), // communication
                        cleanData($data[5])  // commentaires
                    ];
                    
                    $stmt->execute($values);
                    echo "Évaluation migrée pour le prestataire : " . $values[0] . "\n";
                } catch (PDOException $e) {
                    echo "Erreur lors de la migration de l'évaluation pour " . $values[0] . ": " . $e->getMessage() . "\n";
                }
            }
        }
        fclose($handle);
        
        echo "\nMigration terminée avec succès!";
    } else {
        echo "Erreur: Le fichier evaluations.csv n'a pas été trouvé.";
    }
} catch (Exception $e) {
    echo "Erreur : " . $e->getMessage();
}
?>
