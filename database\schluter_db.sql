-- Création de la base de données
CREATE DATABASE IF NOT EXISTS schluter_db;
USE schluter_db;

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    description TEXT,
    icon VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des tickets d'assistance
CREATE TABLE IF NOT EXISTS tickets (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    priority ENUM('low', 'medium', 'high'),
    status ENUM('open', 'in_progress', 'closed') DEFAULT 'open',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- Table des accusés de réception
CREATE TABLE IF NOT EXISTS accuses_reception (
    id INT AUTO_INCREMENT PRIMARY KEY,
    collaborateur VARCHAR(100) NOT NULL,
    responsable VARCHAR(100) NOT NULL,
    date_creation DATE NOT NULL,
    materiel_remis TEXT,
    materiel_recu TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table de l'inventaire
CREATE TABLE IF NOT EXISTS inventaire (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_article VARCHAR(100) NOT NULL,
    type VARCHAR(50),
    quantite INT NOT NULL,
    description TEXT,
    etat VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des évaluations de prestataires
CREATE TABLE IF NOT EXISTS evaluations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prestataire VARCHAR(100) NOT NULL,
    service VARCHAR(100) NOT NULL,
    qualite INT NOT NULL,
    delai INT NOT NULL,
    communication INT NOT NULL,
    commentaires TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des changements de poste
CREATE TABLE IF NOT EXISTS changements_poste (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom_utilisateur VARCHAR(100) NOT NULL,
    ancien_poste VARCHAR(100) NOT NULL,
    nouveau_poste VARCHAR(100),
    date_changement DATE NOT NULL,
    lifecycle VARCHAR(50),
    responsable VARCHAR(100) NOT NULL,
    type ENUM('pc_installes', 'pc_prevus', 'pc_a_prevoir'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table des événements
CREATE TABLE IF NOT EXISTS evenements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    titre VARCHAR(200) NOT NULL,
    description TEXT,
    date_debut DATETIME NOT NULL,
    date_fin DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insertion de données de test pour les utilisateurs
INSERT INTO users (username, password, email, role) VALUES
('admin', '$2y$10$example_hash', '<EMAIL>', 'admin'),
('chris', '$2y$10$example_hash', '<EMAIL>', 'user');

-- Insertion de données de test pour les services
INSERT INTO services (title, description, icon) VALUES
('IT', 'Support informatique et gestion des systèmes', 'fas fa-laptop'),
('RH', 'Ressources humaines et gestion du personnel', 'fas fa-users'),
('Finance', 'Gestion financière et comptabilité', 'fas fa-euro-sign');
