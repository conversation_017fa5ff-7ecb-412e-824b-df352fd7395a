<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>tFPDF</title>
<style type="text/css">
body {font-family:"Times New Roman",serif}
h1 {font:bold 135% Arial,sans-serif; color:#4000A0; margin-bottom:0.9em}
h2 {font:bold 95% Arial,sans-serif; color:#900000; margin-top:1.5em; margin-bottom:1em}
</style>
</head>
<body>
<h1>tFPDF</h1>
<h2>Informations</h2>
Author: <a href="mailto:<EMAIL>?subject=tFPDF">Ian Back</a><br>
License: LGPL
<h2>Description</h2>
This class is a modified version of FPDF that adds UTF-8 support. Moreover, it embeds only
the necessary parts of the fonts that are used in the document, making the file size much
smaller than if the whole fonts were embedded. These features were originally developed
for the <a href="https://mpdf.github.io/" target="_blank">mPDF</a> project.<br>
<br>
To use a Unicode font in your script, pass the font file name as third parameter of
<code>AddFont()</code> and <code>true</code> as fourth parameter. The font may be located
either in the font/unifont directory or directly in the system font folder (in case the
<code>_SYSTEM_TTFONTS</code> constant is defined).
The package ships with the <a href="https://dejavu-fonts.github.io/" target="_blank">DejaVu</a>
font family.<br>
<br>
<strong>Note:</strong> this class requires the mbstring extension.
</body>
</html>
