<?php
session_start();
if (!isset($_SESSION['logged_in']) || !$_SESSION['logged_in']) {
    header('Location: login.php');
    exit();
}

// Connexion à la base de données
$db = new PDO('mysql:host=localhost;dbname=schluter_db', 'root', '', array(
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
));

// Charger les services depuis la base de données
function loadServices() {
    global $db;
    $stmt = $db->query('SELECT title, description, icon FROM services');
    return $stmt->fetchAll(PDO::FETCH_NUM);
}

// Sauvegarder un nouveau service dans la base de données
function saveService($title, $description, $icon) {
    global $db;
    $stmt = $db->prepare('INSERT INTO services (title, description, icon) VALUES (?, ?, ?)');
    $stmt->execute([$title, $description, $icon]);
}

// Supprimer un service de la base de données
function deleteService($title) {
    global $db;
    $stmt = $db->prepare('DELETE FROM services WHERE title = ?');
    $stmt->execute([$title]);
}

// Charger les services pour l'affichage
$services = loadServices();

// Gérer la soumission du formulaire pour ajouter ou supprimer un service
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action']) && $_POST['action'] === 'add') {
        $title = htmlspecialchars($_POST['title']);
        $description = htmlspecialchars($_POST['description']);
        $icon = htmlspecialchars($_POST['icon']);
        saveService($title, $description, $icon);
    } elseif (isset($_POST['action']) && $_POST['action'] === 'delete') {
        $titleToDelete = htmlspecialchars($_POST['title']);
        deleteService($titleToDelete);
    }
    header("Location: index.php");
    exit();
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Portail intranet de Schluter Systems pour accéder aux ressources internes.">
    <title>Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Réinitialisation des styles par défaut */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        /* Styles globaux */
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f4f4;
            color: #333;
            transition: background-color 0.3s ease, color 0.3s ease;
        }
        /* En-tête */
        header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 100;
        }

        header ul {
            list-style: none;
            display: flex;
            justify-content: center;
            width: 100%;
        }

        header ul li {
            margin: 0 20px;
        }

        header ul li a {
            color: white;
            text-decoration: none;
            font-size: 1.1em;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        header ul li a:hover {
            background-color: #f57c00;
        }

        /* Bannière */
        .banner {
            background-color: #f57c00;
            color: white;
            text-align: center;
            padding: 50px 0;
            margin-top: 50px;
        }

        .banner h1 {
            font-size: 2.5em;
            margin-bottom: 0.5em;
        }

        .banner p {
            font-size: 1.2em;
        }

        /* Section À propos */
        .about {
            background-color: #fff;
            padding: 50px;
            text-align: center;
            margin: 30px 0;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .about h2 {
            color: #f57c00;
        }

        .about p {
            font-size: 1.1em;
            color: #555;
        }

        /* Barre de recherche */
        .search-section {
            text-align: center;
            margin: 40px auto;
            max-width: 800px;
        }

        .search-section h2 {
            color: #f57c00;
            margin-bottom: 20px;
            font-size: 1.8em;
        }

        .search-container {
            background: linear-gradient(145deg, #ffffff, #f0f0f0);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .search-container:hover {
            box-shadow: 0 6px 20px rgba(245,124,0,0.15);
            transform: translateY(-2px);
        }

        .search-container input {
            width: 100%;
            padding: 15px 20px; /* Supprimé le padding-left supplémentaire */
            border: 2px solid transparent;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background-color: rgba(255,255,255,0.9);
        }

        .search-container input:focus {
            border-color: #f57c00;
            box-shadow: 0 0 0 3px rgba(245,124,0,0.2);
            outline: none;
        }
        /* Services */
        .services {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            padding: 30px;
            width: 100%;
        }

        .service-card {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: calc(33.333% - 30px);
            padding: 20px;
            text-align: center;
            margin: 15px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer; /* Make the card look clickable */
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .service-card i {
            font-size: 3em;
            color: #f57c00;
            margin-bottom: 15px;
        }

        .service-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .service-card p {
            font-size: 1em;
            color: #555;
        }

        .service-card a {
            text-decoration: none;
            color: inherit;
            display: block;
        }

        /* Animation de chargement */
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .loading.active {
            display: flex;
        }

        .spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #f57c00;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Pied de page */
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            width: 100%;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            header ul {
                flex-direction: column;
            }

            header ul li {
                margin: 10px 0;
            }

            .banner h1 {
                font-size: 2em;
            }

            .banner p {
                font-size: 1em;
            }

            .services {
                flex-direction: column;
                align-items: center;
            }

            .service-card {
                width: 80%;
                margin: 20px 0;
            }
        }
    </style>
    <script>
         function filterServices() {
            const searchText = document.getElementById('searchServices').value.toLowerCase();
            const cards = document.querySelectorAll('.service-card');

            cards.forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();

                if (title.includes(searchText) || description.includes(searchText)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</head>
<body>
    <div class="fixed inset-0 bg-white bg-opacity-80 z-50 hidden items-center justify-center" id="loading">
        <div class="w-12 h-12 border-4 border-schluter-orange border-t-transparent rounded-full animate-spin"></div>
    </div>

    <?php include 'top_bar.php'; ?>

    <div class="relative bg-gradient-to-r from-schluter-gray to-schluter-orange py-16 px-4 sm:px-6 lg:px-8 text-white mt-16">
        <div class="max-w-7xl mx-auto">
            <h1 class="text-4xl font-bold mb-4" data-section="banner-title">
                Bienvenue sur le Portail Intranet
            </h1>
            <p class="text-xl" data-section="banner-text">
                Votre espace dédié pour accéder à toutes les ressources internes.
            </p>
        </div>
    </div>

    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h2 class="text-3xl font-bold text-schluter-orange mb-4" data-section="about-title">
                À propos de Schluter Systems
            </h2>
            <p class="text-gray-600 text-lg leading-relaxed" data-section="about-text">
                Schluter Systems est une entreprise innovante spécialisée dans les solutions pour la construction et la rénovation.
                Nous nous engageons à fournir des produits de haute qualité et un service exceptionnel à nos clients.
            </p>
        </div>
    </div>

    <div class="max-w-7xl mx-auto mt-12 px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-2xl font-bold text-schluter-orange mb-6">
                <i class="fas fa-search mr-2"></i>
                Rechercher un Service
            </h2>
            <div class="relative">
                <input type="text"
                       id="searchServices"
                       placeholder="Tapez le nom ou la description d'un service..."
                       onkeyup="filterServices()"
                       class="w-full px-6 py-4 pr-12 border-2 border-gray-200 rounded-lg focus:ring-2 focus:ring-schluter-orange focus:border-schluter-orange transition duration-200"
                       aria-label="Rechercher un service">
                <i class="fas fa-search absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>

        <div class="mt-12 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($services as $service): ?>
                <div class="transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                    <a href="<?= strtolower($service[0]) ?>.php"
                       class="block bg-white rounded-lg shadow-md overflow-hidden">
                        <div class="p-6 space-y-4">
                            <div class="w-16 h-16 bg-schluter-orange bg-opacity-10 rounded-full flex items-center justify-center mx-auto">
                                <i class="<?= htmlspecialchars($service[2]) ?> text-3xl text-schluter-orange"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-800 text-center">
                                <?= htmlspecialchars($service[0]) ?>
                            </h3>
                            <p class="text-gray-600 text-center">
                                <?= htmlspecialchars($service[1]) ?>
                            </p>
                        </div>
                        <div class="px-6 py-3 bg-gray-50 border-t border-gray-100">
                            <div class="flex items-center justify-center text-schluter-orange hover:text-schluter-orange-dark">
                                <span class="text-sm font-medium">Accéder</span>
                                <i class="fas fa-arrow-right ml-2"></i>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endforeach; ?>
        </div>
        </form>
        </div>
    </div>
<?php include 'bottom_bar.php'; ?>
</body>
</html>
