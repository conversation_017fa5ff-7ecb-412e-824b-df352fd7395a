<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetAutoPageBreak</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetAutoPageBreak</h1>
<code>SetAutoPageBreak(<b>boolean</b> auto [, <b>float</b> margin])</code>
<h2>Description</h2>
Active ou désactive le mode saut de page automatique. En cas d'activation, le second paramètre
représente la distance par rapport au bas de la page qui déclenche le saut. Par défaut, le mode
est activé et la marge vaut 2 cm.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>auto</code></dt>
<dd>
Booléen indiquant si le mode doit être activé.
</dd>
<dt><code>margin</code></dt>
<dd>
Distance par rapport au bas de la page.
</dd>
</dl>
<h2>Voir</h2>
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="acceptpagebreak.htm">AcceptPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
