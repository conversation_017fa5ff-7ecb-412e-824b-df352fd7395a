<?php
try {
    $db = new PDO('mysql:host=localhost', 'root', '', array(
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ));

    // Créer la base de données si elle n'existe pas
    $db->exec('CREATE DATABASE IF NOT EXISTS schluter_db');
    $db->exec('USE schluter_db');

    // Table des évaluations de prestataires
    $db->exec('CREATE TABLE IF NOT EXISTS evaluations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prestataire VARCHAR(100) NOT NULL,
        service VARCHAR(100) NOT NULL,
        qualite INT NOT NULL,
        delai INT NOT NULL,
        communication INT NOT NULL,
        commentaires TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )');

    echo "Base de données et tables créées avec succès!";
} catch(PDOException $e) {
    die("Erreur lors de l'initialisation de la base de données : " . $e->getMessage());
}
