<?php 
include 'top-bar-informatique.php';

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addChangement($nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type) {
    $conn = getDbConnection();
    $sql = "INSERT INTO changements_poste (nom_utilisateur, ancien_poste, nouveau_poste, date_changement, 
            lifecycle, responsable, type) VALUES (?, ?, ?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$nom, $ancien_poste, $nouveau_poste, $date, $lifecycle, $responsable, $type]);
}

function deleteChangement($id) {
    $conn = getDbConnection();
    $sql = "DELETE FROM changements_poste WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['submit'])) {
        addChangement(
            $_POST['nom'],
            $_POST['ancien_poste'],
            $_POST['nouveau_poste'],
            $_POST['date'],
            $_POST['lifecycle'],
            $_POST['responsable'],
            $_POST['type']
        );
    } elseif (isset($_POST['delete'])) {
        deleteChangement($_POST['id']);
    }
}

// Récupérer les données pour chaque type
$conn = getDbConnection();

$sql_installes = "SELECT * FROM changements_poste WHERE type = 'pc_installes' ORDER BY date_changement DESC";
$stmt_installes = $conn->query($sql_installes);
$pc_installes = $stmt_installes->fetchAll(PDO::FETCH_ASSOC);

$sql_prevus = "SELECT * FROM changements_poste WHERE type = 'pc_prevus' ORDER BY date_changement DESC";
$stmt_prevus = $conn->query($sql_prevus);
$pc_prevus = $stmt_prevus->fetchAll(PDO::FETCH_ASSOC);

$sql_a_prevoir = "SELECT * FROM changements_poste WHERE type = 'pc_a_prevoir' ORDER BY date_changement DESC";
$stmt_a_prevoir = $conn->query($sql_a_prevoir);
$pc_a_prevoir = $stmt_a_prevoir->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>End of Life - Gestion des Postes</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<div class="container">
    <h1 class="page-title">Gestion des Postes - End of Life</h1>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-card">
            <h2>Ajouter un Poste</h2>
            <form method="POST" action="End_Of_Life.php">
                <label for="type">Sélectionner :</label>
                <select id="type" name="type" required>
                    <option value="pc_installes">PC Installés</option>
                    <option value="pc_prevus">PC Prévus</option>
                    <option value="pc_a_prevoir">PC à Prévoir</option>
                </select>
                <label for="nom">Nom utilisateur :</label>
                <input type="text" id="nom" name="nom" required>
                <label for="ancien_poste">Ancien poste :</label>
                <input type="text" id="ancien_poste" name="ancien_poste" required>
                <label for="nouveau_poste">Nouveau poste :</label>
                <input type="text" id="nouveau_poste" name="nouveau_poste">
                <label for="date">Date de changement :</label>
                <input type="date" id="date" name="date" required>
                <label for="lifecycle">Lifecycle :</label>
                <input type="text" id="lifecycle" name="lifecycle" required>
                <label for="responsable">Fait par :</label>
                <input type="text" id="responsable" name="responsable" required>
                <button type="submit" name="submit" class="btn-primary">Ajouter</button>
            </form>
        </div>
    </div>

    <!-- Table Section -->
    <h2 class="section-title">Liste des Postes</h2>
    <div class="table-container">
        <h3>PC Installés</h3>
        <table>
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Ancien Poste</th>
                    <th>Nouveau Poste</th>
                    <th>Date de Changement</th>
                    <th>Lifecycle</th>
                    <th>Fait par</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($pc_installes as $changement): ?>
                    <tr>
                        <td><?= htmlspecialchars($changement['nom_utilisateur']) ?></td>
                        <td><?= htmlspecialchars($changement['ancien_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['nouveau_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['date_changement']) ?></td>
                        <td><?= htmlspecialchars($changement['lifecycle']) ?></td>
                        <td><?= htmlspecialchars($changement['responsable']) ?></td>
                        <td>
                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                <button type="submit" name="delete" class="btn-danger">Supprimer</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <h3>PC Prévus</h3>
        <table>
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Ancien Poste</th>
                    <th>Nouveau Poste</th>
                    <th>Date de Changement</th>
                    <th>Lifecycle</th>
                    <th>Fait par</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($pc_prevus as $changement): ?>
                    <tr>
                        <td><?= htmlspecialchars($changement['nom_utilisateur']) ?></td>
                        <td><?= htmlspecialchars($changement['ancien_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['nouveau_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['date_changement']) ?></td>
                        <td><?= htmlspecialchars($changement['lifecycle']) ?></td>
                        <td><?= htmlspecialchars($changement['responsable']) ?></td>
                        <td>
                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                <button type="submit" name="delete" class="btn-danger">Supprimer</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <h3>PC à Prévoir</h3>
        <table>
            <thead>
                <tr>
                    <th>Nom</th>
                    <th>Ancien Poste</th>
                    <th>Nouveau Poste</th>
                    <th>Date de Changement</th>
                    <th>Lifecycle</th>
                    <th>Fait par</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($pc_a_prevoir as $changement): ?>
                    <tr>
                        <td><?= htmlspecialchars($changement['nom_utilisateur']) ?></td>
                        <td><?= htmlspecialchars($changement['ancien_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['nouveau_poste']) ?></td>
                        <td><?= htmlspecialchars($changement['date_changement']) ?></td>
                        <td><?= htmlspecialchars($changement['lifecycle']) ?></td>
                        <td><?= htmlspecialchars($changement['responsable']) ?></td>
                        <td>
                            <form method="POST" action="End_Of_Life.php" style="display:inline;">
                                <input type="hidden" name="id" value="<?= $changement['id'] ?>">
                                <button type="submit" name="delete" class="btn-danger">Supprimer</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php include 'bottom_bar.php'; ?>
</body>
</html>