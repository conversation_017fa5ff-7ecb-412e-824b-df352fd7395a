<?php
// Test des ajustements d'espacement dans la section signatures
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');
include_once('accuse_reception.php');

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test Espacement Signatures</title>";
echo "<style>";
echo "body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; background: linear-gradient(135deg, #8e44ad, #9b59b6); min-height: 100vh; color: white; }";
echo ".container { max-width: 1200px; margin: 0 auto; padding: 40px 20px; }";
echo ".header { background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; text-align: center; margin-bottom: 30px; backdrop-filter: blur(10px); }";
echo ".header h1 { margin: 0; font-size: 2.5em; }";
echo ".card { background: rgba(255,255,255,0.1); padding: 25px; border-radius: 12px; margin-bottom: 25px; backdrop-filter: blur(10px); }";
echo ".card h2 { margin-top: 0; border-bottom: 3px solid #8e44ad; padding-bottom: 10px; }";
echo ".success { background: rgba(46, 204, 113, 0.2); border-left: 4px solid #2ecc71; }";
echo ".info { background: rgba(52, 152, 219, 0.2); border-left: 4px solid #3498db; }";
echo ".warning { background: rgba(243, 156, 18, 0.2); border-left: 4px solid #f39c12; }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }";
echo ".feature-item { background: rgba(255,255,255,0.05); padding: 20px; border-radius: 8px; border-left: 4px solid #8e44ad; }";
echo ".btn { padding: 12px 24px; text-decoration: none; border-radius: 8px; display: inline-block; margin: 8px; font-weight: bold; transition: all 0.3s; text-align: center; color: white; }";
echo ".btn-primary { background: linear-gradient(135deg, #3498db, #2980b9); }";
echo ".btn-success { background: linear-gradient(135deg, #2ecc71, #27ae60); }";
echo ".btn-purple { background: linear-gradient(135deg, #8e44ad, #9b59b6); }";
echo ".btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.3); }";
echo "table { width: 100%; border-collapse: collapse; margin: 20px 0; }";
echo "th, td { padding: 12px; text-align: left; border-bottom: 1px solid rgba(255,255,255,0.2); }";
echo "th { background: rgba(255,255,255,0.1); font-weight: bold; }";
echo ".status-badge { padding: 4px 12px; border-radius: 20px; font-size: 0.9em; font-weight: bold; }";
echo ".status-improved { background: #8e44ad; color: white; }";
echo ".status-fixed { background: #2ecc71; color: white; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";

echo "<div class='header'>";
echo "<h1>✍️ Test Espacement Signatures</h1>";
echo "<p>Validation des ajustements d'espacement dans la section signatures</p>";
echo "</div>";

// Vérification des ajustements
function checkSignatureSpacing() {
    echo "<div class='card success'>";
    echo "<h2>✅ Ajustements Implémentés</h2>";
    
    $fileContent = file_get_contents('accuse_reception.php');
    
    $adjustments = [
        "Espacement après titre SIGNATURES (contenu long)" => strpos($fileContent, "'spacing_after_signature_title' => 6") !== false,
        "Espacement après titre SIGNATURES (contenu normal)" => strpos($fileContent, "'spacing_after_signature_title' => 8") !== false,
        "Position titre boîte signature ajustée" => strpos($fileContent, '$this->SetXY($x, $y - 8)') !== false,
        "Utilisation paramètre espacement" => strpos($fileContent, '$pdf->Ln($layout[\'spacing_after_signature_title\'])') !== false,
        "Commentaire explicatif ajouté" => strpos($fileContent, '// Espacement paramétrable') !== false
    ];
    
    echo "<div class='feature-grid'>";
    foreach ($adjustments as $feature => $status) {
        $statusClass = $status ? 'status-improved' : 'status-warning';
        $statusText = $status ? '✍️ Amélioré' : '⚠️ À vérifier';
        echo "<div class='feature-item'>";
        echo "<strong>$feature</strong><br>";
        echo "<span class='status-badge $statusClass'>$statusText</span>";
        echo "</div>";
    }
    echo "</div>";
    echo "</div>";
}

// Affichage des améliorations d'espacement
function displaySpacingImprovements() {
    echo "<div class='card info'>";
    echo "<h2>📏 Améliorations d'Espacement</h2>";
    
    echo "<table>";
    echo "<tr><th>Élément</th><th>Avant</th><th>Après</th><th>Amélioration</th></tr>";
    
    $improvements = [
        [
            "Espacement après titre \"SIGNATURES\" (normal)", 
            "3mm fixe", 
            "8mm paramétrable", 
            "+167% d'espace"
        ],
        [
            "Espacement après titre \"SIGNATURES\" (long)", 
            "3mm fixe", 
            "6mm paramétrable", 
            "+100% d'espace"
        ],
        [
            "Position titre boîte signature", 
            "-6mm du bord", 
            "-8mm du bord", 
            "+33% de séparation"
        ],
        [
            "Lisibilité générale", 
            "Titre trop proche", 
            "Séparation claire", 
            "Amélioration visuelle"
        ]
    ];
    
    foreach ($improvements as $improvement) {
        echo "<tr>";
        echo "<td><strong>" . $improvement[0] . "</strong></td>";
        echo "<td>" . $improvement[1] . "</td>";
        echo "<td>" . $improvement[2] . "</td>";
        echo "<td>" . $improvement[3] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
    echo "</div>";
}

// Génération de PDFs de test
function generateSignatureTestPDFs() {
    echo "<div class='card warning'>";
    echo "<h2>🧪 Tests de Validation</h2>";
    
    // Données de test pour différents scénarios
    $testData = [
        [
            'ID' => 'SIG1',
            'Collaborateur' => 'Marie Dubois',
            'Responsable' => 'Jean Martin',
            'Date' => date('Y-m-d'),
            'Remis' => 'Ordinateur portable standard avec accessoires.',
            'Recu' => 'Ancien matériel informatique.'
        ],
        [
            'ID' => 'SIG2',
            'Collaborateur' => 'François Müller-Schmidt',
            'Responsable' => 'Stéphane Lefèvre-Durand',
            'Date' => date('Y-m-d'),
            'Remis' => 'Station de travail Dell Precision 7560 avec processeur Intel Xeon W-11955M, 32GB RAM DDR4, SSD NVMe 1TB Samsung, carte graphique NVIDIA RTX A3000, écran 15.6 pouces 4K OLED tactile, station d\'accueil Dell WD19TBS Thunderbolt, double écran Dell UltraSharp U2720Q 27 pouces 4K IPS avec support ergonomique, clavier mécanique Logitech MX Keys, souris de précision Logitech MX Master 3 pour Business, webcam Logitech Brio 4K, casque audio professionnel Jabra Evolve2 85.',
            'Recu' => 'Ancien poste de travail Dell Optiplex 7070 avec processeur Intel Core i7-9700, 16GB RAM DDR4, disque dur hybride 1TB + SSD 256GB, carte graphique intégrée Intel UHD 630, écran Dell P2414H 24 pouces Full HD, clavier Dell KB216 filaire, souris Dell MS116 filaire, haut-parleurs Dell AX210, webcam Logitech C270, casque audio basique.'
        ]
    ];
    
    echo "<div class='feature-grid'>";
    
    foreach ($testData as $index => $data) {
        $contentLength = strlen($data['Remis'] . $data['Recu']);
        $testType = $contentLength > 500 ? 'Long' : 'Normal';
        $expectedSpacing = $contentLength > 500 ? '6mm' : '8mm';
        
        echo "<div class='feature-item'>";
        echo "<h3>✍️ Test " . ($index + 1) . " - Contenu $testType</h3>";
        echo "<p><strong>Longueur :</strong> $contentLength caractères</p>";
        echo "<p><strong>Espacement attendu :</strong> $expectedSpacing après \"SIGNATURES\"</p>";
        echo "<ul>";
        echo "<li>✅ Test espacement titre signatures</li>";
        echo "<li>✅ Test position titre boîte</li>";
        echo "<li>✅ Validation lisibilité générale</li>";
        echo "</ul>";
        
        try {
            $pdf = new FPDF_UTF8();
            $layout = calculateOptimalLayout(null, $contentLength);
            
            $pdf->SetAutoPageBreak(false);
            $pdf->AliasNbPages();
            $pdf->AddPage();
            $pdf->SetMargins($layout['margins'], $layout['margins'], $layout['margins']);
            
            addOptimizedPDFHeader($pdf, true, $contentLength > 500);
            generateOptimizedPDFSection($pdf, $data, true);
            
            $filename = 'test_signature_spacing_' . ($index + 1) . '_' . date('Y-m-d_H-i-s') . '.pdf';
            $pdf->Output('F', $filename);
            
            echo "<a href='$filename' class='btn btn-success'>📄 Télécharger PDF</a>";
            echo "<p style='font-size: 0.9em; margin-top: 10px;'>Espacement appliqué: " . $layout['spacing_after_signature_title'] . "mm</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: #f39c12;'>❌ Erreur : " . $e->getMessage() . "</p>";
        }
        
        echo "</div>";
    }
    
    echo "</div>";
    echo "</div>";
}

// Comparaison visuelle
function displayVisualComparison() {
    echo "<div class='card'>";
    echo "<h2>👁️ Comparaison Visuelle</h2>";
    
    echo "<div class='feature-grid'>";
    
    echo "<div class='feature-item'>";
    echo "<h3>❌ Avant les Ajustements</h3>";
    echo "<pre style='background: rgba(0,0,0,0.2); padding: 15px; border-radius: 5px; font-size: 0.9em;'>";
    echo "MATERIEL RECU\n";
    echo "Description du matériel...\n";
    echo "\n";
    echo "SIGNATURES\n";
    echo "   ↓ 3mm seulement\n";
    echo "┌─────────────────────────┐\n";
    echo "│ Responsable Service Info│ ← Trop proche\n";
    echo "│                         │\n";
    echo "│ ___________________     │\n";
    echo "│      Signature          │\n";
    echo "└─────────────────────────┘";
    echo "</pre>";
    echo "</div>";
    
    echo "<div class='feature-item'>";
    echo "<h3>✅ Après les Ajustements</h3>";
    echo "<pre style='background: rgba(0,0,0,0.2); padding: 15px; border-radius: 5px; font-size: 0.9em;'>";
    echo "MATERIEL RECU\n";
    echo "Description du matériel...\n";
    echo "\n";
    echo "SIGNATURES\n";
    echo "   ↓ 6-8mm selon contenu\n";
    echo "\n";
    echo "┌─────────────────────────┐\n";
    echo "│ Responsable Service Info│ ← Bien séparé\n";
    echo "│                         │\n";
    echo "│ ___________________     │\n";
    echo "│      Signature          │\n";
    echo "└─────────────────────────┘";
    echo "</pre>";
    echo "</div>";
    
    echo "</div>";
    echo "</div>";
}

// Afficher le contenu
checkSignatureSpacing();
displaySpacingImprovements();
displayVisualComparison();
generateSignatureTestPDFs();

echo "<div class='card success'>";
echo "<h2>🎯 Résumé des Ajustements</h2>";
echo "<p><strong>L'espacement dans la section signatures a été optimisé :</strong></p>";
echo "<ul>";
echo "<li>✍️ <strong>Espacement après titre</strong> : 6-8mm selon le type de contenu</li>";
echo "<li>📏 <strong>Position titre boîte</strong> : -8mm pour plus de séparation</li>";
echo "<li>⚙️ <strong>Paramètres configurables</strong> : Ajustement facile via calculateOptimalLayout()</li>";
echo "<li>📄 <strong>Pagination préservée</strong> : Contraintes A4 respectées</li>";
echo "<li>🎨 <strong>Lisibilité améliorée</strong> : Séparation visuelle claire</li>";
echo "</ul>";
echo "<div style='text-align: center; margin-top: 20px;'>";
echo "<a href='accuse_reception.php' class='btn btn-primary'>🔙 Page Principale</a>";
echo " ";
echo "<a href='test_layout_fixes.php' class='btn btn-purple'>🔧 Tests Mise en Page</a>";
echo "</div>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
