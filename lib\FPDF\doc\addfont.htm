<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AddFont</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>AddFont</h1>
<code>AddFont(<b>string</b> family [, <b>string</b> style [, <b>string</b> file [, <b>string</b> dir]]])</code>
<h2>Description</h2>
Importe une police TrueType, OpenType ou Type1 et la rend disponible. Il faut au préalable avoir généré
un fichier de définition de police avec l'utilitaire MakeFont.
<br>
<br>
Le fichier de définition (ainsi que le fichier de police en cas d'incorporation) doit être présent dans :
<ul>
<li>Le répertoire indiqué par le quatrième paeamètre (si ce paramètre est renseigné)</li>
<li>Le répertoire indiqué par la constante <code>FPDF_FONTPATH</code> (si cette constante est définie)</li>
<li>Le répertoire <code>font</code> situé dans le répertoire de <code>fpdf.php</code></li>
</ul>
Si le fichier n'est pas trouvé, l'erreur "Could not include font definition file" est générée.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>family</code></dt>
<dd>
Famille de la police. Le nom peut être choisi arbitrairement. S'il s'agit de celui d'une famille
standard, la police correspondante sera masquée.
</dd>
<dt><code>style</code></dt>
<dd>
Style de la police. Les valeurs possibles sont (indépendamment de la casse) :
<ul>
<li>chaîne vide : normal</li>
<li><code>B</code> : gras</li>
<li><code>I</code> : italique</li>
<li><code>BI</code> ou <code>IB</code> : gras italique</li>
</ul>
La valeur par défaut est le style normal.
</dd>
<dt><code>file</code></dt>
<dd>
Le fichier de définition de police.
<br>
Par défaut, le nom est construit à partir du nom de la famille et du style, en minuscules et
sans espace.
</dd>
<dt><code>dir</code></dt>
<dd>
Le répertoire dans lequel se trouve le fichier de définition.
<br>
Si non renseigné, le répertoire par défaut est utilisé.
</dd>
</dl>
<h2>Exemple</h2>
<div class="doc-source">
<pre><code>$pdf-&gt;AddFont('Comic', 'I');</code></pre>
</div>
est équivalent à :
<div class="doc-source">
<pre><code>$pdf-&gt;AddFont('Comic', 'I', 'comici.php');</code></pre>
</div>
<h2>Voir</h2>
<a href="setfont.htm">SetFont</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
