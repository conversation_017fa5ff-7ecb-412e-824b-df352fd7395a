<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AddPage</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>AddPage</h1>
<code>AddPage([<b>string</b> orientation [, <b>mixed</b> size [, <b>int</b> rotation]]])</code>
<h2>Description</h2>
Ajoute une nouvelle page au document. Si une page était en cours, la méthode Footer() est
appelée pour traiter le pied de page. Puis la page est ajoutée, la position courante
mise en haut à gauche en fonction des marges gauche et haute, et Header() est appelée
pour afficher l'en-tête.
<br>
La police qui était en cours au moment de l'appel est automatiquement restaurée. Il n'est
donc pas nécessaire d'appeler à nouveau SetFont() si vous souhaitez continuer avec la même
police. Même chose pour les couleurs et l'épaisseur du trait.
<br>
L'origine du système de coordonnées est en haut à gauche et les ordonnées croissantes vont
vers le bas.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>orientation</code></dt>
<dd>
Orientation de la page. Les valeurs possibles sont (indépendamment de la casse) :
<ul>
<li><code>P</code> ou <code>Portrait</code></li>
<li><code>L</code> ou <code>Landscape</code></li>
</ul>
La valeur par défaut est celle indiquée dans le constructeur.
</dd>
<dt><code>size</code></dt>
<dd>
Format de la page. Il peut s'agir d'une des valeurs ci-dessous (indépendamment de la casse) :
<ul>
<li><code>A3</code></li>
<li><code>A4</code></li>
<li><code>A5</code></li>
<li><code>Letter</code></li>
<li><code>Legal</code></li>
</ul>
ou bien d'un tableau contenant la largeur et la hauteur (exprimées en unité utilisateur).<br>
<br>
La valeur par défaut est celle indiquée dans le constructeur.
</dd>
<dt><code>rotation</code></dt>
<dd>
Angle de rotation de la page. La valeur doit être un multiple de 90 ; la rotation se fait
dans le sens horaire. La valeur par défaut est <code>0</code>.
</dd>
</dl>
<h2>Voir</h2>
<a href="__construct.htm">__construct</a>,
<a href="header.htm">Header</a>,
<a href="footer.htm">Footer</a>,
<a href="setmargins.htm">SetMargins</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
