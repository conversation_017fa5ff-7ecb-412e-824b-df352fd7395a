<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetAuthor</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetAuthor</h1>
<code>SetAuthor(<b>string</b> author [, <b>boolean</b> isUTF8])</code>
<h2>Description</h2>
Définit l'auteur du document.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>author</code></dt>
<dd>
Le nom de l'auteur.
</dd>
<dt><code>isUTF8</code></dt>
<dd>
Indique si la chaîne est encodée en ISO-8859-1 (<code>false</code>) ou en UTF-8 (<code>true</code>).<br>
Valeur par défaut : <code>false</code>.
</dd>
</dl>
<h2>Voir</h2>
<a href="setcreator.htm">SetCreator</a>,
<a href="setkeywords.htm">SetKeywords</a>,
<a href="setsubject.htm">SetSubject</a>,
<a href="settitle.htm">SetTitle</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
