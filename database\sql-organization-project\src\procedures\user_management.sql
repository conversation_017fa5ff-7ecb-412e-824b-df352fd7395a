-- Procedures for managing user accounts

-- Procedure to create a new user
CREATE PROCEDURE create_user(
    IN p_username VARCHA<PERSON>(50),
    IN p_password VARCHAR(255),
    IN p_email VARCHAR(100),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_department_id INT,
    IN p_role_id INT
)
BEGIN
    INSERT INTO users (username, password, email, first_name, last_name, department_id, role_id)
    VALUES (p_username, p_password, p_email, p_first_name, p_last_name, p_department_id, p_role_id);
END;

-- Procedure to update an existing user
CREATE PROCEDURE update_user(
    IN p_user_id INT,
    IN p_username VARCHAR(50),
    IN p_password VARCHAR(255),
    IN p_email VARCHAR(100),
    IN p_first_name VARCHAR(50),
    IN p_last_name VARCHAR(50),
    IN p_department_id INT,
    IN p_role_id INT
)
BEGIN
    UPDATE users
    SET username = p_username,
        password = p_password,
        email = p_email,
        first_name = p_first_name,
        last_name = p_last_name,
        department_id = p_department_id,
        role_id = p_role_id
    WHERE id = p_user_id;
END;

-- Procedure to delete a user
CREATE PROCEDURE delete_user(
    IN p_user_id INT
)
BEGIN
    DELETE FROM users WHERE id = p_user_id;
END;