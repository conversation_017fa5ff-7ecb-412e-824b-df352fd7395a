<?php
// Script pour ajouter des données de test pour l'historique
error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addAccuseReception($collaborateur, $responsable, $date, $remis, $recu) {
    $conn = getDbConnection();
    
    $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu)
            VALUES (:collaborateur, :responsable, :date, :remis, :recu)";
    
    $stmt = $conn->prepare($sql);
    $result = $stmt->execute([
        ':collaborateur' => $collaborateur,
        ':responsable' => $responsable,
        ':date' => $date,
        ':remis' => $remis,
        ':recu' => $recu
    ]);
    
    if ($result) {
        return $conn->lastInsertId();
    }
    return false;
}

echo "<h1>Ajout de données de test pour l'historique</h1>";

// Données de test avec caractères français
$testData = [
    [
        'collaborateur' => 'François Müller',
        'responsable' => 'Stéphane Lefèvre',
        'date' => '2024-01-15',
        'remis' => 'Ordinateur portable HP EliteBook 840 G8, souris sans fil Logitech, clavier français AZERTY, écran 24" Dell UltraSharp, station d\'accueil USB-C',
        'recu' => 'Ancien ordinateur portable Lenovo ThinkPad, câbles réseau, documentation technique, ancien écran 19"'
    ],
    [
        'collaborateur' => 'Marie-Claire Dubois',
        'responsable' => 'Jean-Pierre Martin',
        'date' => '2024-02-20',
        'remis' => 'iPhone 14 Pro, chargeur Lightning, étui de protection, écouteurs AirPods Pro',
        'recu' => 'Ancien iPhone 11, chargeur usagé, étui défectueux'
    ],
    [
        'collaborateur' => 'Cédric Beaumont',
        'responsable' => 'Stéphane Lefèvre',
        'date' => '2024-03-10',
        'remis' => 'Ordinateur de bureau Dell OptiPlex 7090, clavier et souris Dell, écran 27" 4K, webcam Logitech C920',
        'recu' => null
    ],
    [
        'collaborateur' => 'Élise Château',
        'responsable' => 'Anne-Sophie Durand',
        'date' => '2024-04-05',
        'remis' => null,
        'recu' => 'Ordinateur portable défaillant, chargeur endommagé, sacoche d\'ordinateur'
    ],
    [
        'collaborateur' => 'José García',
        'responsable' => 'Jean-Pierre Martin',
        'date' => '2024-05-12',
        'remis' => 'Tablette iPad Air, Apple Pencil, clavier Magic Keyboard, étui de protection',
        'recu' => 'Ancienne tablette Samsung Galaxy Tab, stylet défectueux'
    ],
    [
        'collaborateur' => 'Anaïs Lefèvre',
        'responsable' => 'Stéphane Lefèvre',
        'date' => '2024-06-18',
        'remis' => 'Ordinateur portable MacBook Pro 14", adaptateur USB-C, souris Magic Mouse, sac de transport',
        'recu' => 'Ancien MacBook Air 13", chargeur MagSafe, souris filaire'
    ]
];

$successCount = 0;
$errorCount = 0;

foreach ($testData as $data) {
    $result = addAccuseReception(
        $data['collaborateur'],
        $data['responsable'],
        $data['date'],
        $data['remis'],
        $data['recu']
    );
    
    if ($result) {
        echo "<p>✅ Ajouté: " . htmlspecialchars($data['collaborateur']) . " (ID: $result)</p>";
        $successCount++;
    } else {
        echo "<p>❌ Erreur pour: " . htmlspecialchars($data['collaborateur']) . "</p>";
        $errorCount++;
    }
}

echo "<h2>Résumé</h2>";
echo "<p>✅ Succès: $successCount</p>";
echo "<p>❌ Erreurs: $errorCount</p>";

echo "<h2>Actions</h2>";
echo "<p><a href='historique.php'>🔍 Voir l'historique</a></p>";
echo "<p><a href='accuse_reception.php'>📝 Ajouter un nouvel accusé</a></p>";
echo "<p><a href='test_historique_pdf.php'>🧪 Tester les fonctions PDF</a></p>";
?>
