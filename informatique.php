<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Portail informatique de Schluter Systems pour la gestion des ressources informatiques.">
    <title>Portail Informatique - Schluter Systems</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .banner {
            background: linear-gradient(135deg, #005ea2, #f57c00);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-bottom: 40px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            margin-left: 0;
            margin-right: 0;
        }

        .banner h1 {
            font-size: 3em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .banner p {
            font-size: 1.3em;
            margin-top: 10px;
            line-height: 1.6;
        }

        .services {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }

        .service-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 300px;
            text-align: center;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .service-card i {
            font-size: 3em;
            color: #f57c00;
            margin-bottom: 15px;
        }

        .service-card h3 {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 10px;
        }

        .service-card p {
            font-size: 1em;
            color: #555;
            margin-bottom: 20px;
        }

        .service-card a {
            display: inline-block;
            padding: 10px 20px;
            background-color: #f57c00;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .service-card a:hover {
            background-color: #e67e22;
        }
    </style>
</head>
<body>
<?php include 'top_bar.php'; ?>

<div class="banner">
    <h1 data-section="banner-title">Portail Informatique</h1>
    <p data-section="banner-text">
        Bienvenue sur le portail informatique de Schluter Systems. 
        Accédez à des outils performants pour gérer efficacement les ressources informatiques 
        et améliorer la productivité de votre équipe.
    </p>
</div>

<div class="services">
    <div class="service-card">
        <i class="fas fa-file-alt"></i>
        <h3>Accusés de Réception</h3>
        <p>Gérez et consultez les accusés de réception pour le matériel informatique.</p>
        <a href="accuse_reception.php" class="btn-primary">Accéder</a>
    </div>
    <div class="service-card">
        <i class="fas fa-exchange-alt"></i>
        <h3>Gestion des Changements de Poste</h3>
        <p>Suivez et gérez les changements de postes informatiques.</p>
        <a href="End_Of_Life.php" class="btn-primary">Accéder</a>
    </div>
    <div class="service-card">
        <i class="fas fa-boxes"></i>
        <h3>Inventaire</h3>
        <p>Consultez et gérez l'inventaire du service informatique.</p>
        <a href="inventaire.php" class="btn-primary">Accéder</a>
    </div>
</div>

<?php include 'bottom_bar.php'; ?>
</body>
</html>