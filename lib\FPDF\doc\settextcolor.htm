<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetTextColor</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetTextColor</h1>
<code>SetTextColor(<b>int</b> r [, <b>int</b> g, <b>int</b> b])</code>
<h2>Description</h2>
Fixe la couleur pour le texte. Elle peut être indiquée en composantes RGB ou en niveau de gris.
La méthode peut être appelée avant que la première page ne soit créée et la valeur est conservée
de page en page.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>r</code></dt>
<dd>
Si <code>g</code> et <code>b</code> sont renseignés, composante de rouge; sinon, indique le
niveau de gris. Valeur comprise entre 0 et 255.
</dd>
<dt><code>g</code></dt>
<dd>
Composante de vert (entre 0 et 255).
</dd>
<dt><code>b</code></dt>
<dd>
Composante de bleu (entre 0 et 255).
</dd>
</dl>
<h2>Voir</h2>
<a href="setdrawcolor.htm">SetDrawColor</a>,
<a href="setfillcolor.htm">SetFillColor</a>,
<a href="text.htm">Text</a>,
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
