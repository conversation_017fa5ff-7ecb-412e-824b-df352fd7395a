<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Footer</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Footer</h1>
<code>Footer()</code>
<h2>Description</h2>
Cette méthode permet de définir le pied de page. Elle est appelée automatiquement par
AddPage() et Close() et ne devrait donc pas être appelée explicitement par l'application.
L'implémentation de Footer() dans FPDF est vide, donc vous devez dériver la classe et
redéfinir la méthode si vous voulez un traitement particulier pour vos pieds de page.
<h2>Exemple</h2>
<div class="doc-source">
<pre><code>class PDF extends FPDF
{
    function Footer()
    {
        // Positionnement à 1,5 cm du bas
        $this-&gt;SetY(-15);
        // Police Arial italique 8
        $this-&gt;SetFont('Arial', 'I', 8);
        // Numéro de page centré
        $this-&gt;Cell(0, 10, 'Page '.$this-&gt;PageNo(), 0, 0, 'C');
    }
}</code></pre>
</div>
<h2>Voir</h2>
<a href="header.htm">Header</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
