<?php
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

$conn = getDbConnection();
$stmt = $conn->query("SELECT prestataire, AVG(qualite) as qualite, AVG(delai) as rapidite, 
                      AVG(communication) as communication, COUNT(*) as count 
                      FROM evaluations 
                      GROUP BY prestataire");
$evaluations = $stmt->fetchAll(PDO::FETCH_ASSOC);

$averages = [
    'Qualité' => [],
    'Rapidité' => [],
    'Communication' => [],
    'Total' => [],
    'Count' => []
];

foreach ($evaluations as $eval) {
    $provider = $eval['prestataire'];
    $averages['Qualité'][$provider] = floatval($eval['qualite']);
    $averages['Rapidité'][$provider] = floatval($eval['rapidite']);
    $averages['Communication'][$provider] = floatval($eval['communication']);
    $averages['Count'][$provider] = intval($eval['count']);
    $averages['Total'][$provider] = ($eval['qualite'] + $eval['rapidite'] + $eval['communication']) / 3;
}

// Pagination for charts
$providersPerPage = 10; // Number of providers per page
$totalProviders = count($averages['Qualité']);
$totalPages = ceil($totalProviders / $providersPerPage);
$currentPage = isset($_GET['page']) ? max(1, min($totalPages, intval($_GET['page']))) : 1;
$startIndex = ($currentPage - 1) * $providersPerPage;
$paginatedProviders = array_slice(array_keys($averages['Qualité']), $startIndex, $providersPerPage);
?>
<?php include 'top-bar-eval.php'; ?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Moyenne de Satisfaction</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 90%;
            margin: 50px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f57c00;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 10px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            width: 80%; /* Increased width */
            max-width: 800px; /* Set a maximum width */
            height: 80%; /* Increased height */
            overflow-y: auto; /* Add scrolling if content overflows */
        }
        .popup.active {
            display: block;
        }
        .popup h2 {
            margin-bottom: 15px;
            color: #333;
        }
        .popup button {
            margin-top: 10px;
            padding: 10px;
            background-color: #f57c00;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .popup button:hover {
            background-color: #e67e22;
        }
        .popup .close-btn {
            background-color: #e74c3c;
            float: right;
        }
        .popup .close-btn:hover {
            background-color: #c0392b;
        }
        .popup canvas {
            width: 100%;
            height: 400px; /* Increased height for better readability */
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Moyenne de Satisfaction par Prestataire</h1>
    <table>
        <thead>
            <tr>
                <th>Prestataire</th>
                <th>Moyenne Qualité</th>
                <th>Moyenne Rapidité</th>
                <th>Moyenne Communication</th>
                <th>Moyenne Totale</th>
                <th>Nombre d'Évaluations</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($paginatedProviders as $provider): ?>
                <tr>
                    <td><?= htmlspecialchars($provider) ?></td>
                    <td><?= number_format($averages['Qualité'][$provider], 2) ?></td>
                    <td><?= number_format($averages['Rapidité'][$provider], 2) ?></td>
                    <td><?= number_format($averages['Communication'][$provider], 2) ?></td>
                    <td><?= number_format($averages['Total'][$provider], 2) ?></td>
                    <td><?= $averages['Count'][$provider] ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>

    <div class="pagination">
        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <?php if ($i !== 1): // Skip the button for page 1 ?>
                <a href="?page=<?= $i ?>" class="<?= $i === $currentPage ? 'active' : '' ?>"><?= $i ?></a>
            <?php endif; ?>
        <?php endfor; ?>
    </div>

    <!-- Button to trigger the popup -->
    <div style="text-align: center; margin-top: 20px;">
        <button onclick="togglePopup()">Afficher le Popup</button>
    </div>
</div>

<!-- Popup structure -->
<div class="popup" id="popup">
    <button class="close-btn" onclick="togglePopup()">Fermer</button>
    <h2>Graphique des Moyennes</h2>
    <label for="searchProvider">Rechercher un prestataire :</label>
    <input type="text" id="searchProvider" placeholder="Rechercher..." oninput="filterProviders()">
    <select id="providerSelect" onchange="updateChart()">
        <option value="" disabled selected>-- Sélectionnez un prestataire --</option>
        <?php foreach ($averages['Qualité'] as $provider => $qualityAverage): ?>
            <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
        <?php endforeach; ?>
    </select>
    <canvas id="providerChart"></canvas>
    <div id="pagination" style="text-align: center; margin-top: 20px;"></div>
</div>

<footer>
    <p>&copy; 2025 Schluter Systems. Tous droits réservés.</p>
</footer>
<script>
    const averages = <?= json_encode($averages) ?>;

    function togglePopup() {
        const popup = document.getElementById('popup');
        popup.classList.toggle('active');
    }

    function updateChart() {
        const provider = document.getElementById('providerSelect').value;
        const ctx = document.getElementById('providerChart').getContext('2d');

        if (window.providerChartInstance) {
            window.providerChartInstance.destroy();
        }

        if (provider && averages['Qualité'][provider]) {
            window.providerChartInstance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Qualité', 'Rapidité', 'Communication', 'Total'],
                    datasets: [{
                        label: `Moyennes pour ${provider}`,
                        data: [
                            averages['Qualité'][provider],
                            averages['Rapidité'][provider],
                            averages['Communication'][provider],
                            averages['Total'][provider]
                        ],
                        backgroundColor: ['#f57c00', '#4caf50', '#2196f3', '#9c27b0'],
                        borderColor: ['#e67e22', '#388e3c', '#1976d2', '#7b1fa2'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.raw.toFixed(2)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Score'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Critères'
                            }
                        }
                    }
                }
            });
        }
    }

    function filterProviders() {
        const searchValue = document.getElementById('searchProvider').value.toLowerCase();
        const providerSelect = document.getElementById('providerSelect');
        const options = providerSelect.options;

        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            const text = option.textContent || option.innerText;
            option.style.display = text.toLowerCase().includes(searchValue) ? '' : 'none';
        }
    }
</script>
</body>
</html>
