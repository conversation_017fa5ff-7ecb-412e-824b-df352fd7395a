<?php
// Test d'insertion de données avec caractères français
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// Fonction de connexion à la base de données (copie de accuse_reception.php)
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        // Forcer l'encodage UTF-8
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addAccuseReception($collaborateur, $responsable, $date, $remis, $recu) {
    $conn = getDbConnection();
    $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu) 
            VALUES (:collaborateur, :responsable, :date, :remis, :recu)";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        ':collaborateur' => $collaborateur,
        ':responsable' => $responsable,
        ':date' => $date,
        ':remis' => $remis,
        ':recu' => $recu
    ]);
    
    return $conn->lastInsertId();
}

// Insérer des données de test avec caractères français
try {
    $id = addAccuseReception(
        'François Müller',
        'Stéphane Lefèvre',
        date('Y-m-d'),
        'Ordinateur portable HP EliteBook, souris sans fil, clavier français AZERTY, écran 24" Dell',
        'Ancien ordinateur portable Lenovo, câbles réseau, documentation technique'
    );
    
    echo "Données de test insérées avec succès (ID: $id)<br>";
    echo "Collaborateur: François Müller<br>";
    echo "Responsable: Stéphane Lefèvre<br>";
    echo "Matériel remis: Ordinateur portable HP EliteBook, souris sans fil, clavier français AZERTY, écran 24\" Dell<br>";
    echo "Matériel reçu: Ancien ordinateur portable Lenovo, câbles réseau, documentation technique<br>";
    echo "<br>";
    echo "Vous pouvez maintenant tester la génération du PDF avec ces données contenant des caractères français.";
    
} catch (Exception $e) {
    echo "Erreur lors de l'insertion: " . $e->getMessage();
}
?>
