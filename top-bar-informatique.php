<?php
$currentPage = basename($_SERVER['PHP_SELF']);
?>
<style>
    .top-bar {
        background-color: #333;
        padding: 15px;
        text-align: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 0; /* Ensure it touches the edges */
    }

    .top-bar .logo {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
        font-size: 1.5em;
        font-weight: bold;
        text-decoration: none;
    }

    .top-bar .logo img {
        height: 40px;
    }

    .top-bar .nav-links {
        display: flex;
        gap: 15px;
        justify-content: center;
        flex-grow: 1;
    }

    .top-bar a {
        color: white;
        text-decoration: none;
        font-size: 1.1em;
        font-weight: bold;
        padding: 10px 15px;
        border-radius: 5px;
        transition: background-color 0.3s ease;
    }

    .top-bar a:hover, .top-bar a.active {
        background-color: #f57c00;
    }

    body {
        margin-top: 70px; /* Adjusted to prevent overlap with the top bar */
    }
</style>
<header class="top-bar">
    <a href="IT.php" class="logo">
        <img src="img/logo_rgb.svg" alt="Schluter Systems Logo">
        Informatique
    </a>
    <div class="nav-links">
        <a href="accuse_reception.php" class="<?= $currentPage === 'accuse_reception.php' ? 'active' : '' ?>">Accusés de Réception</a>
        <a href="End_Of_Life.php" class="<?= $currentPage === 'End_Of_Life.php' ? 'active' : '' ?>">Changements de Poste</a>
        <a href="inventaire.php" class="<?= $currentPage === 'inventaire.php' ? 'active' : '' ?>">Inventaire</a>
    </div>
</header>
