<?php 
include 'top-bar-eval.php';
require_once __DIR__ . '/config/database.php';

// Function to handle database operations
function handleDb($action, $data = null) {
    global $db;
    
    switch ($action) {
        case "read":
            $stmt = $db->query("SELECT * FROM evaluations ORDER BY created_at DESC");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        case "write":
            $stmt = $db->prepare("INSERT INTO evaluations (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)");
            return $stmt->execute($data);
            
        case "getProviders":
            $stmt = $db->query("SELECT DISTINCT prestataire FROM evaluations ORDER BY prestataire");
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
            
        case "getServices":
            $stmt = $db->query("SELECT DISTINCT service FROM evaluations ORDER BY service");
            return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }
    return null;
}

// Handle form submission for evaluations
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['provider'])) {
    $response = ['status' => 'success'];

    if (isset($_POST['provider'], $_POST['serviceType'], $_POST['serviceQuality'],
        $_POST['deliveryTime'], $_POST['communication'])) {
        $data = [
            $_POST['provider'],
            $_POST['serviceType'],
            intval($_POST['serviceQuality']),
            intval($_POST['deliveryTime']),
            intval($_POST['communication']),
            $_POST['comments'] ?? ''
        ];
        try {
            handleDb("write", $data);
        } catch (PDOException $e) {
            $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
            http_response_code(500);
        }
    } else {
        $response = ['status' => 'error', 'message' => 'Invalid form submission.'];
        http_response_code(400);
    }

    echo json_encode($response);
    exit();
}

// Charger les prestataires et services depuis la base de données
$providers = handleDb("getProviders");
$services = handleDb("getServices");
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Évaluation des Prestataires - Schluter Systems</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f4f4;
            color: #333;
            padding: 20px;
        }
        .container {
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: auto;
            position: relative;
            margin-top: 70px; /* Adjust for the fixed top bar */
        }
        h1 {
            color: #f57c00;
            text-align: center;
        }
        .rating-section {
            margin-bottom: 20px;
        }
        .rating-section label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .rating-section select,
        .rating-section textarea {
            width: 100%;
            padding: 10px;
            margin-top: 5px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .rating-section textarea {
            height: 100px;
            resize: vertical;
        }
        .submit-btn {
            display: block;
            width: 100%;
            padding: 15px;
            background-color: #f57c00;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 1em;
            cursor: pointer;
            text-align: center;
        }
        .submit-btn:hover {
            background-color: #e67e22;
        }
        .icon {
            font-size: 24px;
            color: #f57c00;
            position: absolute;
            top: 20px;
            cursor: pointer;
        }
        .back-icon {
            left: 20px;
        }
        .admin-icon {
            right: 20px;
        }
        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            width: 400px;
        }
        .popup.active {
            display: block;
        }
        .popup h2 {
            color: #f57c00;
            text-align: center;
            margin-bottom: 20px;
        }
        .popup label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .popup input,
        .popup select {
            width: calc(100% - 22px);
            padding: 10px;
            margin-top: 5px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .popup button {
            margin-top: 10px;
            padding: 10px;
            background-color: #f57c00;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            width: 100%;
        }
        .popup button:hover {
            background-color: #e67e22;
        }
        .popup .close-btn {
            background-color: #e74c3c;
        }
        .popup .close-btn:hover {
            background-color: #c0392b;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            width: 100%;
        }
        .top-bar {
            background-color: #333;
            padding: 15px;
            text-align: center;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 1000;
        }

        .top-bar a {
            color: white;
            text-decoration: none;
            margin: 0 15px;
            font-size: 1.1em;
            font-weight: bold;
            transition: color 0.3s;
        }

        .top-bar a:hover {
            color: #ffa500;
        }
    </style>
</head>
    <div class="container">
        <h1>Évaluation des Prestataires</h1>
        <form id="evaluationForm">
            <div class="rating-section">
                <label for="provider" data-section="provider">Prestataire évalué :</label>
                <select id="provider" name="provider" required>
                    <option value="">Sélectionnez un prestataire</option>
                    <?php foreach ($providers as $provider): ?>
                        <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="rating-section">
                <label for="serviceType" data-section="serviceType">Service demandé :</label>
                <select id="serviceType" name="serviceType" required>
                    <option value="">Sélectionnez un service</option>
                    <?php foreach ($services as $service): ?>
                        <option value="<?= htmlspecialchars($service) ?>"><?= htmlspecialchars($service) ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="rating-section">
                <label for="serviceQuality" data-section="serviceQuality">Qualité du service :</label>
                <select id="serviceQuality" name="serviceQuality" required>
                    <option value="">Sélectionnez une note</option>
                    <option value="5">5 - Excellent</option>
                    <option value="4">4 - Très bon</option>
                    <option value="3">3 - Bon</option>
                    <option value="2">2 - Moyen</option>
                    <option value="1">1 - Mauvais</option>
                </select>
            </div>
            <div class="rating-section">
                <label for="deliveryTime" data-section="deliveryTime">Délai de livraison :</label>
                <select id="deliveryTime" name="deliveryTime" required>
                    <option value="">Sélectionnez une note</option>
                    <option value="5">5 - Très rapide</option>
                    <option value="4">4 - Rapide</option>
                    <option value="3">3 - Moyen</option>
                    <option value="2">2 - Lent</option>
                    <option value="1">1 - Très lent</option>
                </select>
            </div>
            <div class="rating-section">
                <label for="communication" data-section="communication">Communication :</label>
                <select id="communication" name="communication" required>
                    <option value="">Sélectionnez une note</option>
                    <option value="5">5 - Excellente</option>
                    <option value="4">4 - Très bonne</option>
                    <option value="3">3 - Bonne</option>
                    <option value="2">2 - Moyenne</option>
                    <option value="1">1 - Mauvaise</option>
                </select>
            </div>
            <div class="rating-section">
                <label for="comments" data-section="comments">Commentaires :</label>
                <textarea id="comments" name="comments" placeholder="Vos commentaires ici..."></textarea>
            </div>
            <button type="submit" class="submit-btn" data-section="submit">Soumettre l'évaluation</button>
        </form>
        <i class="fas fa-arrow-left icon back-icon" onclick="window.location.href='evaluation-des-prestataires.php'"></i>
        <i class="fas fa-cogs icon admin-icon" onclick="togglePopup()"></i>
        <div class="popup" id="adminPopup">
            <h2>Gestion des Prestataires et Services</h2>
            <form id="addProviderForm" method="POST">
                <input type="hidden" name="action" value="addProvider">
                <div>
                    <label for="newProvider" data-section="addProvider">Ajouter un prestataire :</label>
                    <input type="text" id="newProvider" name="newItem" placeholder="Nom du prestataire" required>
                    <button type="submit">Ajouter</button>
                </div>
            </form>
            <form id="removeProviderForm" method="POST">
                <input type="hidden" name="action" value="removeProvider">
                <div>
                    <label for="removeProvider" data-section="removeProvider">Supprimer un prestataire :</label>
                    <select id="removeProvider" name="removeItem" required>
                        <option value="">Sélectionnez un prestataire</option>
                        <?php foreach ($providers as $provider): ?>
                            <option value="<?= htmlspecialchars($provider) ?>"><?= htmlspecialchars($provider) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <button type="submit">Supprimer</button>
                </div>
            </form>
            <form id="addServiceForm" method="POST">
                <input type="hidden" name="action" value="addService">
                <div>
                    <label for="newService" data-section="newService">Ajouter un service :</label>
                    <input type="text" id="newService" name="newItem" placeholder="Nom du service" required>
                    <button type="submit">Ajouter</button>
                </div>
            </form>
            <form id="removeServiceForm" method="POST">
                <input type="hidden" name="action" value="removeService">
                <div>
                    <label for="removeService" data-section="removeService">Supprimer un service :</label>
                    <select id="removeService" name="removeItem" required>
                        <option value="">Sélectionnez un service</option>
                        <?php foreach ($services as $service): ?>
                            <option value="<?= htmlspecialchars($service) ?>"><?= htmlspecialchars($service) ?></option>
                        <?php endforeach; ?>
                    </select>
                    <button type="submit">Supprimer</button>
                </div>
            </form>
            <button class="close-btn" onclick="togglePopup()" data-section="close">Fermer</button>
        </div>
    </div>
    
    <script>
            document.addEventListener('DOMContentLoaded', () => {
            changeLanguage(localStorage.getItem('language') || 'fr');

            document.getElementById('evaluationForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('addProviderForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('removeProviderForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('addServiceForm').addEventListener('submit', handleFormSubmit);
            document.getElementById('removeServiceForm').addEventListener('submit', handleFormSubmit);
        });

        function handleFormSubmit(event) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            fetch('evaluation.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert('Action effectuée avec succès!');
                    location.reload();
                }
            })
            .catch(error => console.error('Error:', error));
        }

        function togglePopup() {
            document.getElementById('adminPopup').classList.toggle('active');
        }

        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
