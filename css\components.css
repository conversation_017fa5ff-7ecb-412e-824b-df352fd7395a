/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    animation: loading 1.5s infinite ease-in-out;
    z-index: 1000;
}

@keyframes loading {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* Notification System */
.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: var(--border-radius);
    background: white;
    box-shadow: var(--box-shadow);
    transform: translateY(100px);
    opacity: 0;
    transition: var(--transition);
    z-index: 1000;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.success {
    background: var(--success-color);
    color: white;
}

.notification.error {
    background: var(--danger-color);
    color: white;
}

.notification.info {
    background: var(--primary-color);
    color: white;
}
