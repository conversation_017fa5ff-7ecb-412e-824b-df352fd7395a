<?php
// Vérifier si l'utilisateur est connecté et est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}
?>

<style>
    .admin-top-bar {
        background-color: #1a237e;
        padding: 15px;
        text-align: center;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .admin-top-bar .title {
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
        font-size: 1.5em;
        font-weight: bold;
        margin-left: 20px;
    }

    .admin-top-bar .nav-buttons {
        display: flex;
        gap: 15px;
        margin-right: 20px;
    }

    .admin-top-bar .admin-btn {
        color: white;
        text-decoration: none;
        font-size: 1.1em;
        font-weight: bold;
        padding: 8px 20px;
        border-radius: 5px;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.2);
    }

    .admin-top-bar .admin-btn:hover {
        background-color: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
    }

    .admin-top-bar .admin-btn i {
        margin-right: 8px;
    }

    /* Ajuster l'espacement du contenu sous la barre */
    body {
        padding-top: 80px;
    }
</style>

<div class="admin-top-bar">
    <div class="title">
        <i class="fas fa-shield-alt"></i>
        Administration
    </div>
    <div class="nav-buttons">
        <a href="IT.php" class="admin-btn">
            <i class="fas fa-arrow-left"></i> Retour à IT
        </a>
    </div>
</div>

<!-- Ajouter Font Awesome pour les icônes -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
