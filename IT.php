<?php
require_once('config/database.php');

// Récupérer les statistiques
function getStats() {
    global $db;
    $stats = [];    // Total des articles en inventaire
    $stmt = $db->query("SELECT COALESCE(SUM(quantite), 0) as total_items, COUNT(*) as unique_items FROM inventaire");
    $inventory = $stmt->fetch(PDO::FETCH_ASSOC);
    $stats['inventory'] = $inventory;    // Changements de poste du mois en cours et du mois précédent
    $stmt = $db->query("SELECT 
        COUNT(*) as total,
        type,
        MONTH(date_changement) as mois
    FROM changements_poste 
    WHERE MONTH(date_changement) IN (MONTH(CURRENT_DATE()), MONTH(CURRENT_DATE() - INTERVAL 1 MONTH))
    GROUP BY type, MONTH(date_changement)");
    $changes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Séparer les stats par mois
    $stats['changes'] = array_filter($changes, function($change) {
        return $change['mois'] == date('n');
    });
    $stats['changes_prev'] = array_filter($changes, function($change) {
        return $change['mois'] == date('n', strtotime('-1 month'));
    });

    // Nombre d'accusés de réception du mois
    $stmt = $db->query("SELECT COUNT(*) as total FROM accuses_reception 
                        WHERE MONTH(date_creation) = MONTH(CURRENT_DATE())");
    $stats['ar'] = $stmt->fetch(PDO::FETCH_ASSOC);

    return $stats;
}

$stats = getStats();
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Portail informatique de Schluter Systems pour la gestion des ressources informatiques.">
    <title>Portail Informatique - Schluter Systems</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        schluter: {
                            orange: '#f57c00',
                            'orange-dark': '#e65100',
                            blue: '#005ea2',
                            'blue-dark': '#004c87',
                            gray: '#333333',
                            'gray-light': '#f4f4f4'
                        }
                    }
                }
            }
        }
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <?php include 'top_bar.php'; ?>

    <main class="container mx-auto px-4 py-8">
        <!-- En-tête avec statistiques -->
        <div class="bg-gradient-to-r from-schluter-blue to-schluter-orange rounded-lg shadow-lg p-8 mb-8 text-white">
            <h1 class="text-4xl font-bold mb-6 text-center">Portail Informatique</h1>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Statistiques d'inventaire -->
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Articles en inventaire</p>
                            <h3 class="text-3xl font-bold"><?= $stats['inventory']['total_items'] ?></h3>
                        </div>
                        <i class="fas fa-box text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75"><?= $stats['inventory']['unique_items'] ?> articles uniques</p>
                </div>

                <!-- Changements de poste -->
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Changements ce mois</p>
                            <h3 class="text-3xl font-bold">
                                <?= array_sum(array_column($stats['changes'], 'total')) ?>
                            </h3>
                        </div>
                        <i class="fas fa-exchange-alt text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">
                        <?= array_sum(array_column($stats['changes_prev'], 'total')) ?> le mois dernier
                    </p>
                </div>

                <!-- Accusés de réception -->
                <div class="bg-white/20 rounded-lg p-6 backdrop-blur-sm">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-lg opacity-90">Accusés de réception</p>
                            <h3 class="text-3xl font-bold"><?= $stats['ar']['total'] ?></h3>
                        </div>
                        <i class="fas fa-file-signature text-4xl opacity-75"></i>
                    </div>
                    <p class="mt-2 text-sm opacity-75">Ce mois-ci</p>
                </div>
            </div>
        </div>

        <!-- Grille des services -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <!-- Inventaire -->
            <a href="inventaire.php" class="transform hover:scale-105 transition-transform duration-300">
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold text-schluter-blue">Inventaire</h3>
                        <i class="fas fa-warehouse text-schluter-orange text-2xl"></i>
                    </div>
                    <p class="text-gray-600">Gérez l'inventaire du matériel informatique</p>
                </div>
            </a>

            <!-- Changements de poste -->
            <a href="End_Of_Life.php" class="transform hover:scale-105 transition-transform duration-300">
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold text-schluter-blue">Suivis des changements de poste</h3>
                        <i class="fas fa-history text-schluter-orange text-2xl"></i>
                    </div>
                    <p class="text-gray-600">Suivi des équipements en fin de vie</p>
                </div>
            </a>

            <!-- Accuse de reception -->
            <a href="accuse_reception.php" class="transform hover:scale-105 transition-transform duration-300">
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold text-schluter-blue">Accusés de reception</h3>
                        <i class="fas fa-clock-rotate-left text-schluter-orange text-2xl"></i>
                    </div>
                    <p class="text-gray-600">Création et suivi des accusés de réception</p>
                </div>
            </a>

            <!-- Administration -->
            <a href="admin.php" class="transform hover:scale-105 transition-transform duration-300">
                <div class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-xl font-semibold text-schluter-blue">Administration</h3>
                        <i class="fas fa-cog text-schluter-orange text-2xl"></i>
                    </div>
                    <p class="text-gray-600">Gérez les paramètres du système</p>
                </div>
            </a>
        </div>
    </main>

    <script>
        // Animation des cartes au chargement
        document.addEventListener('DOMContentLoaded', () => {
            gsap.from('.service-card', {
                duration: 0.6,
                y: 50,
                opacity: 0,
                stagger: 0.1,
                ease: 'power2.out'
            });
        });
    </script>
</body>
</html>