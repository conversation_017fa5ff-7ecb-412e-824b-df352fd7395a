<?php
// Génération de PDFs de test pour valider les optimisations de pagination
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');
include_once('accuse_reception.php');

// Données de test avec différentes longueurs de contenu
$testData = [
    [
        'ID' => 'TEST1',
        'Collaborateur' => '<PERSON>',
        'Responsable' => '<PERSON>',
        'Date' => date('Y-m-d'),
        'Remis' => 'Ordinateur portable HP EliteBook, souris sans fil.',
        'Recu' => 'Ancien ordinateur portable.'
    ],
    [
        'ID' => 'TEST2',
        'Collaborateur' => 'Sophie <PERSON>',
        'Responsable' => '<PERSON>',
        'Date' => date('Y-m-d'),
        'Remis' => 'Ordinateur portable HP EliteBook 840 G8 avec processeur Intel Core i7, 16GB RAM, SSD 512GB, écran 14 pouces Full HD, souris sans fil Logitech MX Master 3, clavier sans fil français AZERTY, station d\'accueil USB-C avec ports multiples, écran externe Dell UltraSharp 24 pouces 4K, câbles HDMI et USB-C, chargeur d\'origine 65W, sacoche de transport professionnelle.',
        'Recu' => 'Ancien ordinateur portable Lenovo ThinkPad T480 avec processeur Intel Core i5, 8GB RAM, disque dur 500GB, écran 14 pouces, souris filaire basique, clavier filaire, ancien chargeur 45W, câbles réseau Ethernet, documentation technique obsolète, CD d\'installation Windows 7.'
    ],
    [
        'ID' => 'TEST3',
        'Collaborateur' => 'François Müller',
        'Responsable' => 'Stéphane Lefèvre',
        'Date' => date('Y-m-d'),
        'Remis' => 'Station de travail Dell Precision 7560 avec processeur Intel Xeon W-11955M, 32GB RAM DDR4, SSD NVMe 1TB Samsung, carte graphique NVIDIA RTX A3000, écran 15.6 pouces 4K OLED tactile, station d\'accueil Dell WD19TBS Thunderbolt, double écran Dell UltraSharp U2720Q 27 pouces 4K IPS avec support ergonomique, clavier mécanique Logitech MX Keys, souris de précision Logitech MX Master 3 pour Business, webcam Logitech Brio 4K, casque audio professionnel Jabra Evolve2 85, haut-parleurs Logitech Z623, tapis de souris ergonomique, support d\'ordinateur portable ajustable, chargeur d\'origine 130W, câbles Thunderbolt 4, HDMI 2.1, USB-C, adaptateurs multiples, sacoche de transport renforcée, licence Microsoft Office 365 Business Premium, licence Adobe Creative Suite, antivirus Bitdefender GravityZone Business Security.',
        'Recu' => 'Ancien poste de travail Dell Optiplex 7070 avec processeur Intel Core i7-9700, 16GB RAM DDR4, disque dur hybride 1TB + SSD 256GB, carte graphique intégrée Intel UHD 630, écran Dell P2414H 24 pouces Full HD, clavier Dell KB216 filaire, souris Dell MS116 filaire, haut-parleurs Dell AX210, webcam Logitech C270, casque audio basique, ancien chargeur 90W, câbles VGA et DVI, adaptateur USB vers Ethernet, documentation utilisateur, CD d\'installation divers, ancien antivirus McAfee, licences logiciels expirées, périphériques USB obsolètes, câbles réseau Cat5e, hub USB 2.0, lecteur CD/DVD externe, disque dur externe 500GB, clé USB 32GB, support écran basique.'
    ]
];

// Générer les PDFs de test
foreach ($testData as $index => $data) {
    $contentLength = strlen($data['Remis'] . $data['Recu']);
    $testType = $contentLength > 500 ? 'Long' : ($contentLength > 200 ? 'Moyen' : 'Court');
    
    echo "<h3>Génération PDF Test " . ($index + 1) . " - Contenu $testType ($contentLength caractères)</h3>";
    
    try {
        $pdf = new FPDF_UTF8();
        
        // Calculer la longueur du contenu pour optimiser les marges
        $layout = calculateOptimalLayout(null, $contentLength);
        
        $pdf->SetAutoPageBreak(false);
        $pdf->AliasNbPages();
        $pdf->AddPage();
        $pdf->SetMargins($layout['margins'], $layout['margins'], $layout['margins']);
        
        // En-tête optimisé
        addOptimizedPDFHeader($pdf, true, $contentLength > 500);
        
        // Contenu optimisé
        generateOptimizedPDFSection($pdf, $data, true);
        
        // Vérifier si on dépasse la page
        $currentY = $pdf->GetY();
        $pageHeight = 297;
        $bottomMargin = 30;
        
        if ($currentY > ($pageHeight - $bottomMargin)) {
            echo "<p style='color: orange;'>⚠️ Contenu dépasse - Régénération en mode ultra-compact</p>";
            
            // Régénérer en mode ultra-compact
            $pdf = new FPDF_UTF8();
            $pdf->SetAutoPageBreak(false);
            $pdf->AliasNbPages();
            $pdf->AddPage();
            $pdf->SetMargins(18, 18, 18);
            
            addOptimizedPDFHeader($pdf, true, true);
            generateOptimizedPDFSection($pdf, $data, true);
            
            $finalY = $pdf->GetY();
            if ($finalY > ($pageHeight - $bottomMargin)) {
                echo "<p style='color: red;'>❌ Contenu trop long même en mode ultra-compact</p>";
            } else {
                echo "<p style='color: green;'>✅ Contenu ajusté avec succès en mode ultra-compact</p>";
            }
        } else {
            echo "<p style='color: green;'>✅ Contenu tient parfaitement sur une page</p>";
        }
        
        // Informations de fin
        $remainingSpace = ($pageHeight - $bottomMargin) - $pdf->GetY();
        if ($remainingSpace > 15) {
            $pdf->Ln(5);
            $pdf->setDrawColorFromPalette('border');
            $pdf->SetLineWidth(0.2);
            $pdf->Line($layout['margins'], $pdf->GetY(), 210 - $layout['margins'], $pdf->GetY());
            $pdf->Ln(4);
            
            $pdf->SetFont('Arial', 'I', 8);
            $pdf->setColorFromPalette('text_light');
            $pdf->Cell(0, 4, 'Document généré le ' . date('d/m/Y à H:i') . ' - Test pagination', 0, 1, 'C');
        }
        
        $filename = 'test_pagination_' . strtolower($testType) . '_' . date('Y-m-d_H-i-s') . '.pdf';
        $pdf->Output('F', $filename);
        
        echo "<p>📄 <strong>Fichier généré :</strong> <a href='$filename' style='color: #3498db;'>$filename</a></p>";
        echo "<p><strong>Espace utilisé :</strong> " . round($pdf->GetY()) . "mm / " . ($pageHeight - $bottomMargin) . "mm disponibles</p>";
        echo "<p><strong>Espace restant :</strong> " . round($remainingSpace) . "mm</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Erreur : " . $e->getMessage() . "</p>";
    }
    
    echo "<hr>";
}

// Générer un PDF de comparaison noir/blanc vs couleur
echo "<h3>Test Impression Noir/Blanc vs Couleur</h3>";

try {
    // Version optimisée pour impression N&B
    $pdf_nb = new FPDF_UTF8();
    $pdf_nb->SetAutoPageBreak(false);
    $pdf_nb->AliasNbPages();
    $pdf_nb->AddPage();
    $pdf_nb->SetMargins(22, 22, 22);
    
    addOptimizedPDFHeader($pdf_nb, true);
    generateOptimizedPDFSection($pdf_nb, $testData[1], true);
    
    $filename_nb = 'test_impression_nb_' . date('Y-m-d_H-i-s') . '.pdf';
    $pdf_nb->Output('F', $filename_nb);
    
    echo "<p>🖨️ <strong>Version Noir/Blanc :</strong> <a href='$filename_nb' style='color: #3498db;'>$filename_nb</a></p>";
    echo "<p>✅ Optimisé pour impression monochrome avec contrastes renforcés</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Erreur génération N&B : " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h2>📊 Résumé des Tests</h2>";
echo "<ul>";
echo "<li>✅ <strong>Contenu Court :</strong> Mise en page standard avec espacement confortable</li>";
echo "<li>✅ <strong>Contenu Moyen :</strong> Optimisations automatiques (marges, police, espacement)</li>";
echo "<li>✅ <strong>Contenu Long :</strong> Mode compact avec ajustements maximaux</li>";
echo "<li>✅ <strong>Impression N&B :</strong> Contrastes optimisés pour monochrome</li>";
echo "<li>✅ <strong>Pagination :</strong> Contrôle strict pour tenir sur une page A4</li>";
echo "</ul>";

echo "<p><a href='test_optimized_pagination.php' style='background: #3498db; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔙 Retour aux tests</a></p>";
?>
