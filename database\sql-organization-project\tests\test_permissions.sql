-- Test cases for permissions functions

-- Test case 1: Check if super_admin has access to all departments
SELECT has_access('super_admin', 'DAF') AS super_admin_access_daf,
       has_access('super_admin', 'DRH') AS super_admin_access_drh;

-- Test case 2: Check if admin has access to their own department
SELECT has_access('admin', 'Ventes Internes') AS admin_access_ventes_internes;

-- Test case 3: Check if manager has access to their team's department
SELECT has_access('manager', 'Production') AS manager_access_production;

-- Test case 4: Check if user has access to their department
SELECT has_access('user', 'ADV') AS user_access_adv;

-- Test case 5: Check if user does not have access to another department
SELECT has_access('user', 'DRH') AS user_access_drh;

-- Test case 6: Check if roles without specific permissions are restricted
SELECT has_access('guest', 'DAF') AS guest_access_daf;