<?php
// Test de la fonction de suppression
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Test de la fonction de suppression actuelle
function testDeleteFunction($deleteId) {
    echo "<h3>Test de suppression pour ID: $deleteId</h3>";
    
    try {
        $conn = getDbConnection();
        
        // Vérifier si l'enregistrement existe avant suppression
        $checkSql = "SELECT * FROM accuses_reception WHERE id = :id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute([':id' => $deleteId]);
        $record = $checkStmt->fetch();
        
        if (!$record) {
            echo "❌ Erreur: Aucun enregistrement trouvé avec l'ID $deleteId<br>";
            return false;
        }
        
        echo "✅ Enregistrement trouvé:<br>";
        echo "- Collaborateur: " . $record['collaborateur'] . "<br>";
        echo "- Responsable: " . $record['responsable'] . "<br>";
        echo "- Date: " . $record['date_creation'] . "<br>";
        
        // Tenter la suppression
        $sql = "DELETE FROM accuses_reception WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([':id' => $deleteId]);
        
        if ($result && $stmt->rowCount() > 0) {
            echo "✅ Suppression réussie! Nombre de lignes affectées: " . $stmt->rowCount() . "<br>";
            return true;
        } else {
            echo "❌ Erreur: Aucune ligne supprimée<br>";
            return false;
        }
        
    } catch (PDOException $e) {
        echo "❌ Erreur SQL: " . $e->getMessage() . "<br>";
        return false;
    }
}

// Lister les enregistrements disponibles
function listRecords() {
    echo "<h3>Enregistrements disponibles:</h3>";
    
    try {
        $conn = getDbConnection();
        $sql = "SELECT id, collaborateur, responsable, date_creation FROM accuses_reception ORDER BY date_creation DESC";
        $stmt = $conn->query($sql);
        $records = $stmt->fetchAll();
        
        if (empty($records)) {
            echo "Aucun enregistrement trouvé.<br>";
            return;
        }
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>Action</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . $record['date_creation'] . "</td>";
            echo "<td><a href='?test_delete=" . $record['id'] . "' onclick='return confirm(\"Êtes-vous sûr de vouloir supprimer cet enregistrement?\")'>🗑️ Tester suppression</a></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "❌ Erreur lors de la récupération des données: " . $e->getMessage() . "<br>";
    }
}

// Traitement des paramètres
if (isset($_GET['test_delete'])) {
    $deleteId = intval($_GET['test_delete']);
    if ($deleteId > 0) {
        testDeleteFunction($deleteId);
        echo "<br><a href='test_delete_function.php'>← Retour à la liste</a><br>";
    } else {
        echo "❌ ID invalide<br>";
    }
} else {
    listRecords();
}

echo "<br><hr><br>";
echo "<h3>Diagnostic du problème dans accuse_reception.php:</h3>";
echo "<ul>";
echo "<li>❌ URL de redirection incorrecte: <code>http://localhost/sauvé/accuse_reception.php</code></li>";
echo "<li>❌ Manque de gestion d'erreurs</li>";
echo "<li>❌ Pas de vérification de l'existence de l'enregistrement</li>";
echo "<li>❌ Pas de confirmation avant suppression</li>";
echo "<li>❌ Pas de message de feedback à l'utilisateur</li>";
echo "</ul>";

echo "<h3>Solutions recommandées:</h3>";
echo "<ul>";
echo "<li>✅ Corriger l'URL de redirection</li>";
echo "<li>✅ Ajouter une gestion d'erreurs complète</li>";
echo "<li>✅ Vérifier l'existence avant suppression</li>";
echo "<li>✅ Ajouter une confirmation JavaScript</li>";
echo "<li>✅ Implémenter des messages de feedback</li>";
echo "</ul>";
?>
