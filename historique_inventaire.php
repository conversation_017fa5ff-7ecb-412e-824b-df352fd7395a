<?php
require_once('config/database.php');

// Charger l'inventaire depuis la base de données
function loadInventaire() {
    global $conn;
    $sql = "SELECT * FROM inventaire ORDER BY id";
    $stmt = $conn->query($sql);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Fonction de recherche
function rechercherInventaire($terme) {
    global $conn;
    $terme = "%$terme%";
    $sql = "SELECT * FROM inventaire 
            WHERE nom_article LIKE ? 
            OR type LIKE ? 
            OR description LIKE ? 
            OR etat LIKE ?
            ORDER BY id";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$terme, $terme, $terme, $terme]);
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

$termeRecherche = isset($_GET['recherche']) ? $_GET['recherche'] : '';
$resultatsRecherche = $termeRecherche ? rechercherInventaire($termeRecherche) : loadInventaire();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique de l'Inventaire</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<?php include 'top_bar.php'; ?>
<div class="container">
    <h1 class="page-title">Historique de l'Inventaire</h1>

    <!-- Search Section -->
    <div class="form-section">
        <form action="" method="get" class="form-card">
            <label for="recherche">Rechercher :</label>
            <input type="text" id="recherche" name="recherche" placeholder="Rechercher..." value="<?= htmlspecialchars($termeRecherche) ?>" required>
            <button type="submit" class="btn-primary">Rechercher</button>
        </form>
    </div>

    <!-- Table Section -->
    <h2 class="section-title">Liste des Articles</h2>
    <div class="table-container">
        <?php if (!empty($resultatsRecherche)) : ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nom de l'article</th>
                    <th>Type</th>
                    <th>Quantité</th>
                    <th>Description</th>
                    <th>État</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($resultatsRecherche as $row) : ?>
                <tr>
                    <td><?= htmlspecialchars($row['id']) ?></td>
                    <td><?= htmlspecialchars($row['nom_article']) ?></td>
                    <td><?= htmlspecialchars($row['type']) ?></td>
                    <td><?= htmlspecialchars($row['quantite']) ?></td>
                    <td><?= htmlspecialchars($row['description']) ?></td>
                    <td><?= htmlspecialchars($row['etat']) ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else : ?>
            <p>Aucun résultat trouvé.</p>
        <?php endif; ?>
    </div>

    <!-- Back Button -->
    <div style="text-align: right; margin-top: 20px;">
        <a href="inventaire.php" class="btn-primary" style="text-decoration: none;">Retour à l'Inventaire</a>
    </div>
</div>
<?php include 'bottom_bar.php'; ?>
</body>
</html>
