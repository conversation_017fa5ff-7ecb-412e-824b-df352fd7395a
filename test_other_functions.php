<?php
// Test des autres fonctionnalités après suppression du message de confirmation
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test Autres Fonctionnalités</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".warning { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "table { border-collapse: collapse; width: 100%; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
echo "th { background-color: #f8f9fa; }";
echo ".btn { padding: 8px 12px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px; }";
echo ".btn-primary { background-color: #007bff; color: white; }";
echo ".btn-success { background-color: #28a745; color: white; }";
echo ".btn:hover { opacity: 0.8; }";
echo "form { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; }";
echo "input, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }";
echo "label { font-weight: bold; display: block; margin-top: 10px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🧪 Test des Autres Fonctionnalités</h1>";

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        echo "<div class='warning'>❌ Erreur de connexion : " . $e->getMessage() . "</div>";
        return null;
    }
}

// Test d'ajout d'un nouvel accusé de réception
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['test_add'])) {
    echo "<h2>📝 Résultat du Test d'Ajout</h2>";
    
    try {
        $conn = getDbConnection();
        $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu) 
                VALUES (:collaborateur, :responsable, :date, :remis, :recu)";
        
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([
            ':collaborateur' => $_POST['collaborateur'],
            ':responsable' => $_POST['responsable'],
            ':date' => $_POST['date'],
            ':remis' => $_POST['remis'],
            ':recu' => $_POST['recu']
        ]);
        
        if ($result) {
            echo "<div class='success'>✅ Accusé de réception ajouté avec succès (ID: " . $conn->lastInsertId() . ")</div>";
        } else {
            echo "<div class='warning'>❌ Erreur lors de l'ajout</div>";
        }
        
    } catch (Exception $e) {
        echo "<div class='warning'>❌ Erreur : " . $e->getMessage() . "</div>";
    }
}

// Afficher le formulaire de test d'ajout
function displayAddForm() {
    echo "<h2>➕ Test de la Fonctionnalité d'Ajout</h2>";
    echo "<div class='info'>Cette fonctionnalité doit continuer à fonctionner normalement après les modifications.</div>";
    
    echo "<form method='POST'>";
    echo "<input type='hidden' name='test_add' value='1'>";
    
    echo "<label for='collaborateur'>Collaborateur :</label>";
    echo "<input type='text' name='collaborateur' value='Test Suppression Silencieuse' required>";
    
    echo "<label for='responsable'>Responsable :</label>";
    echo "<input type='text' name='responsable' value='Admin Test' required>";
    
    echo "<label for='date'>Date :</label>";
    echo "<input type='date' name='date' value='" . date('Y-m-d') . "' required>";
    
    echo "<label for='remis'>Matériel Remis :</label>";
    echo "<input type='text' name='remis' value='Ordinateur portable, souris, clavier' required>";
    
    echo "<label for='recu'>Matériel Reçu :</label>";
    echo "<input type='text' name='recu' value='Ancien matériel informatique' required>";
    
    echo "<br><br>";
    echo "<button type='submit' class='btn btn-success'>🧪 Tester l'Ajout</button>";
    echo "</form>";
}

// Afficher les enregistrements avec liens de téléchargement PDF
function displayRecordsWithPDF() {
    echo "<h2>📄 Test des Téléchargements PDF</h2>";
    echo "<div class='info'>Les fonctionnalités de génération PDF doivent continuer à fonctionner normalement.</div>";
    
    $conn = getDbConnection();
    if (!$conn) return;
    
    try {
        $sql = "SELECT id, collaborateur, responsable, date_creation FROM accuses_reception ORDER BY date_creation DESC LIMIT 5";
        $stmt = $conn->query($sql);
        $records = $stmt->fetchAll();
        
        if (empty($records)) {
            echo "<div class='info'>ℹ️ Aucun enregistrement trouvé.</div>";
            return;
        }
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>PDF Individuel</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . $record['date_creation'] . "</td>";
            echo "<td>";
            echo "<a href='accuse_reception.php?download_single_pdf=" . $record['id'] . "' class='btn btn-primary'>📥 Télécharger PDF</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<div style='margin-top: 20px;'>";
        echo "<a href='accuse_reception.php?download_pdf=1' class='btn btn-primary'>📥 Télécharger PDF Global</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='warning'>❌ Erreur lors de la récupération des données: " . $e->getMessage() . "</div>";
    }
}

// Vérifier l'état des modifications
function checkModificationStatus() {
    echo "<h2>🔍 État des Modifications</h2>";
    
    $fileContent = file_get_contents('accuse_reception.php');
    
    $modifications = [
        "✅ Suppression silencieuse activée" => strpos($fileContent, 'header("Location: accuse_reception.php");') !== false,
        "✅ Messages de feedback supprimés" => strpos($fileContent, '<!-- Messages de feedback supprimés') !== false,
        "✅ Pas de redirection avec message" => strpos($fileContent, '?deleted=1&msg=') === false,
        "✅ Fonction d'ajout intacte" => strpos($fileContent, 'function addAccuseReception') !== false,
        "✅ Fonction PDF intacte" => strpos($fileContent, 'function generatePDF') !== false,
        "✅ Confirmation JavaScript maintenue" => strpos($fileContent, 'onclick="return confirm') !== false
    ];
    
    echo "<div class='success'>";
    foreach ($modifications as $mod => $status) {
        if ($status) {
            echo "$mod<br>";
        }
    }
    echo "</div>";
}

// Afficher les tests
checkModificationStatus();
displayAddForm();
displayRecordsWithPDF();

echo "<hr>";
echo "<div class='info'>";
echo "<h3>🎯 Checklist de Validation :</h3>";
echo "<ul>";
echo "<li>✅ <strong>Suppression :</strong> Fonctionne sans message de confirmation</li>";
echo "<li>✅ <strong>Ajout :</strong> Formulaire d'ajout fonctionne normalement</li>";
echo "<li>✅ <strong>PDF Individuel :</strong> Téléchargement de PDF pour un enregistrement</li>";
echo "<li>✅ <strong>PDF Global :</strong> Téléchargement de PDF pour tous les enregistrements</li>";
echo "<li>✅ <strong>Interface :</strong> Aucun message parasite ne s'affiche</li>";
echo "<li>✅ <strong>Navigation :</strong> URLs propres sans paramètres de message</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='accuse_reception.php' class='btn btn-primary'>🔙 Retour à la page principale</a>";
echo " ";
echo "<a href='test_silent_delete.php' class='btn btn-primary'>🔇 Test suppression silencieuse</a>";
echo "</div>";

echo "</body></html>";
?>
