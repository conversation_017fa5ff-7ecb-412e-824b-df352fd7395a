<?php
// Génération d'un PDF de test avec les améliorations visuelles
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');

// Classe FPDF étendue pour supporter l'UTF-8 avec améliorations visuelles (copie simplifiée)
class FPDF_UTF8_Test extends FPDF
{
    // Couleurs de l'entreprise Schlüter Systems
    private $colors = array(
        'primary' => array(0, 70, 150),      // Bleu Schlüter
        'secondary' => array(230, 126, 34),   // Orange accent
        'text_dark' => array(44, 62, 80),     // Gris foncé
        'text_light' => array(127, 140, 141), // Gris clair
        'border' => array(189, 195, 199),     // Gris bordure
        'background' => array(236, 240, 241)  // Gris fond
    );
    
    function setColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetTextColor($color[0], $color[1], $color[2]);
        }
    }
    
    function setFillColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetFillColor($color[0], $color[1], $color[2]);
        }
    }
    
    function setDrawColorFromPalette($colorName)
    {
        if (isset($this->colors[$colorName])) {
            $color = $this->colors[$colorName];
            $this->SetDrawColor($color[0], $color[1], $color[2]);
        }
    }
    
    function addSignatureBox($x, $y, $width, $height, $title)
    {
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.8);
        $this->Rect($x, $y, $width, $height);
        
        // Titre de la boîte
        $this->SetXY($x, $y - 8);
        $this->SetFont('Arial', 'B', 10);
        $this->setColorFromPalette('text_dark');
        $this->Cell($width, 6, $title, 0, 0, 'C');
        
        // Ligne pour signature
        $this->SetLineWidth(0.3);
        $this->Line($x + 10, $y + $height - 15, $x + $width - 10, $y + $height - 15);
        
        // Texte "Signature"
        $this->SetXY($x + 10, $y + $height - 12);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');
        $this->Cell($width - 20, 4, 'Signature', 0, 0, 'C');
    }
    
    // Pied de page amélioré
    function Footer()
    {
        $this->SetY(-20);
        $this->setDrawColorFromPalette('border');
        $this->SetLineWidth(0.3);
        $this->Line(25, $this->GetY(), 185, $this->GetY());
        
        $this->SetY(-15);
        $this->SetFont('Arial', 'I', 8);
        $this->setColorFromPalette('text_light');
        
        // Informations de l'entreprise à gauche
        $this->Cell(0, 5, 'Schluter Systems - Service Informatique', 0, 0, 'L');
        
        // Numéro de page à droite
        $this->Cell(0, 5, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'R');
    }
}

// Créer un PDF de test avec toutes les améliorations
$pdf = new FPDF_UTF8_Test();
$pdf->SetAutoPageBreak(true, 35);
$pdf->AliasNbPages();
$pdf->AddPage();
$pdf->SetMargins(25, 25, 25);

// Logo de l'entreprise (placeholder textuel)
$pdf->SetXY(150, 15);
$pdf->SetFont('Arial', 'B', 12);
$pdf->setColorFromPalette('primary');
$pdf->Cell(35, 8, 'SCHLUTER', 0, 1, 'C');
$pdf->SetX(150);
$pdf->SetFont('Arial', '', 10);
$pdf->Cell(35, 6, 'SYSTEMS', 0, 0, 'C');

// Titre principal avec style amélioré
$pdf->SetY(25);
$pdf->SetFont('Arial', 'B', 28);
$pdf->setColorFromPalette('primary');
$pdf->Cell(0, 15, 'ACCUSE DE RECEPTION', 0, 1, 'L');

// Sous-titre
$pdf->SetFont('Arial', '', 12);
$pdf->setColorFromPalette('text_light');
$pdf->Cell(0, 8, 'Service Informatique - Schluter Systems', 0, 1, 'L');

// Ligne de séparation élégante
$pdf->Ln(5);
$pdf->setDrawColorFromPalette('primary');
$pdf->SetLineWidth(1.5);
$pdf->Line(25, $pdf->GetY(), 120, $pdf->GetY());
$pdf->Ln(15);

// Informations du collaborateur dans un encadré
$pdf->setFillColorFromPalette('background');
$pdf->setDrawColorFromPalette('border');
$pdf->SetLineWidth(0.5);
$pdf->Rect(25, $pdf->GetY(), 160, 25, 'DF');

$pdf->SetFont('Arial', 'B', 14);
$pdf->setColorFromPalette('primary');
$pdf->Cell(0, 8, 'INFORMATIONS GENERALES', 0, 1, 'L');
$pdf->Ln(2);

// Données de test
$pdf->SetFont('Arial', 'B', 11);
$pdf->setColorFromPalette('text_dark');
$pdf->Cell(40, 6, 'Collaborateur :', 0, 0, 'L');
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(70, 6, 'François Müller', 0, 0, 'L');

$pdf->SetFont('Arial', 'B', 11);
$pdf->Cell(25, 6, 'Responsable :', 0, 0, 'L');
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(0, 6, 'Stéphane Lefèvre', 0, 1, 'L');

// Date
$pdf->SetFont('Arial', 'B', 11);
$pdf->setColorFromPalette('text_dark');
$pdf->Cell(40, 6, 'Date :', 0, 0, 'L');
$pdf->SetFont('Arial', '', 11);
$pdf->Cell(0, 6, date('d/m/Y'), 0, 1, 'L');

$pdf->Ln(15);

// Section "Matériel remis"
$pdf->SetFont('Arial', 'B', 13);
$pdf->setColorFromPalette('secondary');
$pdf->Cell(0, 8, 'MATERIEL REMIS', 0, 1, 'L');

$pdf->setDrawColorFromPalette('secondary');
$pdf->SetLineWidth(0.8);
$pdf->Line(25, $pdf->GetY(), 70, $pdf->GetY());
$pdf->Ln(8);

$pdf->SetFont('Arial', '', 11);
$pdf->setColorFromPalette('text_dark');
$pdf->MultiCell(0, 6, 'Ordinateur portable HP EliteBook 840 G8, souris sans fil Logitech, clavier français AZERTY, écran externe Dell 24" UltraSharp, station d\'accueil USB-C, câbles de connexion, documentation utilisateur.', 0, 'L');
$pdf->Ln(12);

// Section "Matériel reçu"
$pdf->SetFont('Arial', 'B', 13);
$pdf->setColorFromPalette('secondary');
$pdf->Cell(0, 8, 'MATERIEL RECU', 0, 1, 'L');

$pdf->setDrawColorFromPalette('secondary');
$pdf->SetLineWidth(0.8);
$pdf->Line(25, $pdf->GetY(), 65, $pdf->GetY());
$pdf->Ln(8);

$pdf->SetFont('Arial', '', 11);
$pdf->setColorFromPalette('text_dark');
$pdf->MultiCell(0, 6, 'Ancien ordinateur portable Lenovo ThinkPad T480, chargeur d\'origine, souris filaire, câbles réseau Ethernet, documentation technique obsolète.', 0, 'L');
$pdf->Ln(20);

// Section signatures améliorée
$pdf->SetFont('Arial', 'B', 13);
$pdf->setColorFromPalette('primary');
$pdf->Cell(0, 8, 'SIGNATURES', 0, 1, 'L');
$pdf->Ln(5);

// Boîtes de signature élégantes
$signatureY = $pdf->GetY();
$pdf->addSignatureBox(25, $signatureY, 75, 35, 'Responsable Service Informatique');
$pdf->addSignatureBox(110, $signatureY, 75, 35, 'Collaborateur');

$pdf->SetY($signatureY + 45);

// Informations supplémentaires en bas de page
$pdf->Ln(10);
$pdf->setDrawColorFromPalette('border');
$pdf->SetLineWidth(0.3);
$pdf->Line(25, $pdf->GetY(), 185, $pdf->GetY());
$pdf->Ln(8);

$pdf->SetFont('Arial', 'I', 9);
$pdf->setColorFromPalette('text_light');
$pdf->Cell(0, 5, 'Document généré automatiquement le ' . date('d/m/Y à H:i'), 0, 1, 'C');
$pdf->Cell(0, 5, 'Schlüter-Systems - Service Informatique', 0, 1, 'C');

// Générer le PDF
$filename = 'test_pdf_ameliore_' . date('Y-m-d_H-i-s') . '.pdf';
$pdf->Output('F', $filename);

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head><meta charset='UTF-8'><title>PDF Généré</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 40px; text-align: center;'>";
echo "<h1>🎨 PDF Amélioré Généré avec Succès !</h1>";
echo "<div style='background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h2>✅ Fichier créé : $filename</h2>";
echo "<p>Le PDF a été généré avec toutes les améliorations visuelles :</p>";
echo "<ul style='text-align: left; display: inline-block;'>";
echo "<li>🎨 Palette de couleurs Schlüter Systems</li>";
echo "<li>📐 Mise en page professionnelle</li>";
echo "<li>🔤 Typographie améliorée</li>";
echo "<li>🏢 Branding cohérent</li>";
echo "<li>✍️ Zones de signature élégantes</li>";
echo "<li>📄 Pied de page informatif</li>";
echo "</ul>";
echo "</div>";
echo "<a href='$filename' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>📄 Télécharger le PDF</a>";
echo "<br><br>";
echo "<a href='test_enhanced_pdf.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔙 Retour aux tests</a>";
echo "</body></html>";
?>
