<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Tableaux</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Tableaux</h1>
Ce tutoriel montre différentes manières de réaliser des tableaux.
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

class </span>PDF <span class="kw">extends </span>FPDF
<span class="kw">{
</span><span class="cmt">// Chargement des données
</span><span class="kw">function </span>LoadData<span class="kw">(</span>$file<span class="kw">)
{
    </span><span class="cmt">// Lecture des lignes du fichier
    </span>$lines <span class="kw">= </span>file<span class="kw">(</span>$file<span class="kw">);
    </span>$data <span class="kw">= array();
    foreach(</span>$lines <span class="kw">as </span>$line<span class="kw">)
        </span>$data<span class="kw">[] = </span>explode<span class="kw">(</span><span class="str">';'</span><span class="kw">,</span>trim<span class="kw">(</span>$line<span class="kw">));
    return </span>$data<span class="kw">;
}

</span><span class="cmt">// Tableau simple
</span><span class="kw">function </span>BasicTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// En-tête
    </span><span class="kw">foreach(</span>$header <span class="kw">as </span>$col<span class="kw">)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>7<span class="kw">,</span>$col<span class="kw">,</span>1<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Données
    </span><span class="kw">foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        foreach(</span>$row <span class="kw">as </span>$col<span class="kw">)
            </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>40<span class="kw">,</span>6<span class="kw">,</span>$col<span class="kw">,</span>1<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    }
}

</span><span class="cmt">// Tableau amélioré
</span><span class="kw">function </span>ImprovedTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// Largeurs des colonnes
    </span>$w <span class="kw">= array(</span>40<span class="kw">, </span>35<span class="kw">, </span>45<span class="kw">, </span>40<span class="kw">);
    </span><span class="cmt">// En-tête
    </span><span class="kw">for(</span>$i<span class="kw">=</span>0<span class="kw">;</span>$i<span class="kw">&lt;</span>count<span class="kw">(</span>$header<span class="kw">);</span>$i<span class="kw">++)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>$i<span class="kw">],</span>7<span class="kw">,</span>$header<span class="kw">[</span>$i<span class="kw">],</span>1<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Données
    </span><span class="kw">foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>0<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>0<span class="kw">],</span><span class="str">'LR'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>1<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>1<span class="kw">],</span><span class="str">'LR'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>2<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>2<span class="kw">],</span>0<span class="kw">,</span><span class="str">','</span><span class="kw">,</span><span class="str">' '</span><span class="kw">),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>3<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>3<span class="kw">],</span>0<span class="kw">,</span><span class="str">','</span><span class="kw">,</span><span class="str">' '</span><span class="kw">),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    }
    </span><span class="cmt">// Trait de terminaison
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>array_sum<span class="kw">(</span>$w<span class="kw">),</span>0<span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'T'</span><span class="kw">);
}

</span><span class="cmt">// Tableau coloré
</span><span class="kw">function </span>FancyTable<span class="kw">(</span>$header<span class="kw">, </span>$data<span class="kw">)
{
    </span><span class="cmt">// Couleurs, épaisseur du trait et police grasse
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>255<span class="kw">,</span>0<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>255<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetDrawColor<span class="kw">(</span>128<span class="kw">,</span>0<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetLineWidth<span class="kw">(</span>.3<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">);
    </span><span class="cmt">// En-tête
    </span>$w <span class="kw">= array(</span>40<span class="kw">, </span>35<span class="kw">, </span>45<span class="kw">, </span>40<span class="kw">);
    for(</span>$i<span class="kw">=</span>0<span class="kw">;</span>$i<span class="kw">&lt;</span>count<span class="kw">(</span>$header<span class="kw">);</span>$i<span class="kw">++)
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>$i<span class="kw">],</span>7<span class="kw">,</span>$header<span class="kw">[</span>$i<span class="kw">],</span>1<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">,</span>true<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Restauration des couleurs et de la police
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>224<span class="kw">,</span>235<span class="kw">,</span>255<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">);
    </span><span class="cmt">// Données
    </span>$fill <span class="kw">= </span>false<span class="kw">;
    foreach(</span>$data <span class="kw">as </span>$row<span class="kw">)
    {
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>0<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>0<span class="kw">],</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>1<span class="kw">],</span>6<span class="kw">,</span>$row<span class="kw">[</span>1<span class="kw">],</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>2<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>2<span class="kw">],</span>0<span class="kw">,</span><span class="str">','</span><span class="kw">,</span><span class="str">' '</span><span class="kw">),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">[</span>3<span class="kw">],</span>6<span class="kw">,</span>number_format<span class="kw">(</span>$row<span class="kw">[</span>3<span class="kw">],</span>0<span class="kw">,</span><span class="str">','</span><span class="kw">,</span><span class="str">' '</span><span class="kw">),</span><span class="str">'LR'</span><span class="kw">,</span>0<span class="kw">,</span><span class="str">'R'</span><span class="kw">,</span>$fill<span class="kw">);
        </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
        </span>$fill <span class="kw">= !</span>$fill<span class="kw">;
    }
    </span><span class="cmt">// Trait de terminaison
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>array_sum<span class="kw">(</span>$w<span class="kw">),</span>0<span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'T'</span><span class="kw">);
}
}

</span>$pdf <span class="kw">= new </span>PDF<span class="kw">();
</span><span class="cmt">// Titres des colonnes
</span>$header <span class="kw">= array(</span><span class="str">'Pays'</span><span class="kw">, </span><span class="str">'Capitale'</span><span class="kw">, </span><span class="str">'Superficie (km²)'</span><span class="kw">, </span><span class="str">'Pop. (milliers)'</span><span class="kw">);
</span><span class="cmt">// Chargement des données
</span>$data <span class="kw">= </span>$pdf<span class="kw">-&gt;</span>LoadData<span class="kw">(</span><span class="str">'pays.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>14<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>BasicTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>ImprovedTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>FancyTable<span class="kw">(</span>$header<span class="kw">,</span>$data<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto5.php' target='_blank' class='demo'>[Exécuter]</a></p>
Un tableau n'étant qu'un ensemble de cellules, il est naturel de les construire à partir de
ces dernières. Le premier est réalisé de la manière la plus élémentaire qui soit : de simples
cellules encadrées, toutes de la même taille et alignées à gauche. Le résultat est
rudimentaire mais très rapide à obtenir.
<br>
<br>
Le deuxième tableau apporte quelques améliorations : chaque colonne a sa propre largeur, les
titres sont centrés et les nombres cadrés à droite. De plus, les traits horizontaux ont été
enlevés. Ceci est réalisé grâce au paramètre <code>border</code> de la méthode <a href='../doc/cell.htm'>Cell()</a>, qui
permet de spécifier quels bords de la cellule doivent être tracés. Dans le cas présent, on
désire les bords gauches (<code>L</code>) et droits (<code>R</code>). Reste le problème du trait
horizontal final pour refermer le tableau. Il y a deux possibilités : soit tester dans la
boucle si on se trouve sur la dernière ligne, auquel cas on utilise <code>LRB</code> pour le
paramètre <code>border</code> ; soit, comme c'est fait ici, ajouter le trait une fois la boucle
terminée.
<br>
<br>
Le troisième tableau est semblable au deuxième mais utilise des couleurs. Il suffit pour cela
de préciser les couleurs de fond, de texte et de trait souhaitées. L'alternance de couleur
pour les lignes est obtenue en utilisant des cellules à fond alternativement coloré et
transparent.
</body>
</html>
