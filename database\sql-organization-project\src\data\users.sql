-- Insertion des utilisateurs initiaux
INSERT INTO users (username, password, email, first_name, last_name, department_id, role_id)
VALUES
('admin_user', 'hashed_password_1', '<EMAIL>', 'Admin', 'User', (SELECT id FROM departments WHERE name = 'Direction Générale'), (SELECT id FROM roles WHERE name = 'super_admin')),
('manager_user', 'hashed_password_2', '<EMAIL>', 'Manager', 'User', (SELECT id FROM departments WHERE name = 'DRH'), (SELECT id FROM roles WHERE name = 'manager')),
('standard_user', 'hashed_password_3', '<EMAIL>', 'Standard', 'User', (SELECT id FROM departments WHERE name = 'Ventes Internes'), (SELECT id FROM roles WHERE name = 'user')),
('tech_support', 'hashed_password_4', '<EMAIL>', 'Tech', 'Support', (SELECT id FROM departments WHERE name = 'Service Technique'), (SELECT id FROM roles WHERE name = 'admin'));