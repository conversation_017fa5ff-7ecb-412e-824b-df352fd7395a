<?php
include 'top_bar.php';

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Get events from database
$conn = getDbConnection();
$stmt = $conn->query("SELECT * FROM evenements ORDER BY date_debut");
$events = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Convert events to JSON for FullCalendar
$calendarEvents = array_map(function($event) {
    return [
        'title' => $event['titre'],
        'start' => $event['date_debut'],
        'end' => $event['date_fin']
    ];
}, $events);

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Événements Internes - Schluter Systems</title>
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css' rel='stylesheet' />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f4f4;
            color: #333;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        .events {
            padding: 50px;
            text-align: center;
            margin-top: 60px; /* Adjust for the fixed top bar */
        }

        .footer {
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            width: 100%;
        }

        @media (max-width: 768px) {
            .top-bar a {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
<?php include 'top_bar.php'; ?>

<div class="events">
    <h1>Événements Internes</h1>
    <div id='calendar'></div>
</div>

<?php include 'bottom_bar.php'; ?>

<script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js'></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var calendarEl = document.getElementById('calendar');
        var calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            events: <?php echo json_encode($calendarEvents); ?>
        });
        calendar.render();
    });

    window.addEventListener('scroll', () => {
        const backToTop = document.getElementById('backToTop');
        backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
    });

    function scrollToTop() {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    }
</script>
</body>
</html>
