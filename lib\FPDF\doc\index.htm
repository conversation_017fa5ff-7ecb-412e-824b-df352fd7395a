<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Documentation</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Documentation</h1>
<a href="__construct.htm">__construct</a> - constructeur<br>
<a href="acceptpagebreak.htm">AcceptPageBreak</a> - accepte ou non un saut de page automatique<br>
<a href="addfont.htm">AddFont</a> - ajoute une nouvelle police<br>
<a href="addlink.htm">AddLink</a> - crée un lien interne<br>
<a href="addpage.htm">AddPage</a> - ajoute une nouvelle page<br>
<a href="aliasnbpages.htm">AliasNbPages</a> - définit un alias pour le nombre de pages<br>
<a href="cell.htm">Cell</a> - imprime une cellule<br>
<a href="close.htm">Close</a> - termine le document<br>
<a href="error.htm">Error</a> - erreur fatale<br>
<a href="footer.htm">Footer</a> - pied de page<br>
<a href="getpageheight.htm">GetPageHeight</a> - renvoie la hauteur de la page courante<br>
<a href="getpagewidth.htm">GetPageWidth</a> - renvoie la largeur de la page courante<br>
<a href="getstringwidth.htm">GetStringWidth</a> - calcule la longueur d'une chaîne<br>
<a href="getx.htm">GetX</a> - renvoie la position x courante<br>
<a href="gety.htm">GetY</a> - renvoie la position y courante<br>
<a href="header.htm">Header</a> - en-tête<br>
<a href="image.htm">Image</a> - imprime une image<br>
<a href="line.htm">Line</a> - trace une ligne<br>
<a href="link.htm">Link</a> - place un lien<br>
<a href="ln.htm">Ln</a> - saut de ligne<br>
<a href="multicell.htm">MultiCell</a> - imprime du texte avec saut de ligne<br>
<a href="output.htm">Output</a> - sauve ou envoie le document<br>
<a href="pageno.htm">PageNo</a> - numéro de page<br>
<a href="rect.htm">Rect</a> - trace un rectangle<br>
<a href="setauthor.htm">SetAuthor</a> - définit l'auteur du document<br>
<a href="setautopagebreak.htm">SetAutoPageBreak</a> - fixe le mode saut de page automatique<br>
<a href="setcompression.htm">SetCompression</a> - active ou désactive la compression<br>
<a href="setcreator.htm">SetCreator</a> - définit le créateur du document<br>
<a href="setdisplaymode.htm">SetDisplayMode</a> - fixe le mode d'affichage<br>
<a href="setdrawcolor.htm">SetDrawColor</a> - définit la couleur de tracé<br>
<a href="setfillcolor.htm">SetFillColor</a> - définit la couleur de remplissage<br>
<a href="setfont.htm">SetFont</a> - fixe la police<br>
<a href="setfontsize.htm">SetFontSize</a> - fixe la taille de la police<br>
<a href="setkeywords.htm">SetKeywords</a> - définit les mots-clés associés au document<br>
<a href="setleftmargin.htm">SetLeftMargin</a> - fixe la marge gauche<br>
<a href="setlinewidth.htm">SetLineWidth</a> - fixe l'épaisseur des traits<br>
<a href="setlink.htm">SetLink</a> - définit la destination d'un lien interne<br>
<a href="setmargins.htm">SetMargins</a> - fixe les marges<br>
<a href="setrightmargin.htm">SetRightMargin</a> - fixe la marge droite<br>
<a href="setsubject.htm">SetSubject</a> - définit le sujet du document<br>
<a href="settextcolor.htm">SetTextColor</a> - définit la couleur du texte<br>
<a href="settitle.htm">SetTitle</a> - définit le titre du document<br>
<a href="settopmargin.htm">SetTopMargin</a> - fixe la marge haute<br>
<a href="setx.htm">SetX</a> - fixe la position x courante<br>
<a href="setxy.htm">SetXY</a> - fixe les positions x et y courantes<br>
<a href="sety.htm">SetY</a> - fixe la position y courante et restaure éventuellement x<br>
<a href="text.htm">Text</a> - imprime une chaîne<br>
<a href="write.htm">Write</a> - imprime du texte en mode flot<br>
</body>
</html>
