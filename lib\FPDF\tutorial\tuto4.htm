<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Multi-colonnes</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Multi-colonnes</h1>
Cet exemple est une variation du précédent montrant comment formater le texte sur plusieurs
colonnes.
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

class </span>PDF <span class="kw">extends </span>FPDF
<span class="kw">{
protected </span>$col <span class="kw">= </span>0<span class="kw">; </span><span class="cmt">// Colonne courante
</span><span class="kw">protected </span>$y0<span class="kw">;      </span><span class="cmt">// Ordonnée du début des colonnes

</span><span class="kw">function </span>Header<span class="kw">()
{
    </span><span class="cmt">// En-tête
    </span><span class="kw">global </span>$titre<span class="kw">;

    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span>15<span class="kw">);
    </span>$w <span class="kw">= </span>$<span class="kw">this-&gt;</span>GetStringWidth<span class="kw">(</span>$titre<span class="kw">)+</span>6<span class="kw">;
    </span>$<span class="kw">this-&gt;</span>SetX<span class="kw">((</span>210<span class="kw">-</span>$w<span class="kw">)/</span>2<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetDrawColor<span class="kw">(</span>0<span class="kw">,</span>80<span class="kw">,</span>180<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>230<span class="kw">,</span>230<span class="kw">,</span>0<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>220<span class="kw">,</span>50<span class="kw">,</span>50<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetLineWidth<span class="kw">(</span>1<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>$w<span class="kw">,</span>9<span class="kw">,</span>$titre<span class="kw">,</span>1<span class="kw">,</span>1<span class="kw">,</span><span class="str">'C'</span><span class="kw">,</span>true<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">(</span>10<span class="kw">);
    </span><span class="cmt">// Sauvegarde de l'ordonnée
    </span>$<span class="kw">this-&gt;</span>y0 <span class="kw">= </span>$<span class="kw">this-&gt;</span>GetY<span class="kw">();
}

function </span>Footer<span class="kw">()
{
    </span><span class="cmt">// Pied de page
    </span>$<span class="kw">this-&gt;</span>SetY<span class="kw">(-</span>15<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">'I'</span><span class="kw">,</span>8<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetTextColor<span class="kw">(</span>128<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>10<span class="kw">,</span><span class="str">'Page '</span><span class="kw">.</span>$<span class="kw">this-&gt;</span>PageNo<span class="kw">(),</span>0<span class="kw">,</span>0<span class="kw">,</span><span class="str">'C'</span><span class="kw">);
}

function </span>SetCol<span class="kw">(</span>$col<span class="kw">)
{
    </span><span class="cmt">// Positionnement sur une colonne
    </span>$<span class="kw">this-&gt;</span>col <span class="kw">= </span>$col<span class="kw">;
    </span>$x <span class="kw">= </span>10<span class="kw">+</span>$col<span class="kw">*</span>65<span class="kw">;
    </span>$<span class="kw">this-&gt;</span>SetLeftMargin<span class="kw">(</span>$x<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetX<span class="kw">(</span>$x<span class="kw">);
}

function </span>AcceptPageBreak<span class="kw">()
{
    </span><span class="cmt">// Méthode autorisant ou non le saut de page automatique
    </span><span class="kw">if(</span>$<span class="kw">this-&gt;</span>col<span class="kw">&lt;</span>2<span class="kw">)
    {
        </span><span class="cmt">// Passage à la colonne suivante
        </span>$<span class="kw">this-&gt;</span>SetCol<span class="kw">(</span>$<span class="kw">this-&gt;</span>col<span class="kw">+</span>1<span class="kw">);
        </span><span class="cmt">// Ordonnée en haut
        </span>$<span class="kw">this-&gt;</span>SetY<span class="kw">(</span>$<span class="kw">this-&gt;</span>y0<span class="kw">);
        </span><span class="cmt">// On reste sur la page
        </span><span class="kw">return </span>false<span class="kw">;
    }
    else
    {
        </span><span class="cmt">// Retour en première colonne
        </span>$<span class="kw">this-&gt;</span>SetCol<span class="kw">(</span>0<span class="kw">);
        </span><span class="cmt">// Saut de page
        </span><span class="kw">return </span>true<span class="kw">;
    }
}

function </span>TitreChapitre<span class="kw">(</span>$num<span class="kw">, </span>$libelle<span class="kw">)
{
    </span><span class="cmt">// Titre
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Arial'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>12<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>SetFillColor<span class="kw">(</span>200<span class="kw">,</span>220<span class="kw">,</span>255<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>6<span class="kw">,</span><span class="str">"Chapitre </span>$num<span class="str"> : </span>$libelle<span class="str">"</span><span class="kw">,</span>0<span class="kw">,</span>1<span class="kw">,</span><span class="str">'L'</span><span class="kw">,</span>true<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">(</span>4<span class="kw">);
    </span><span class="cmt">// Sauvegarde de l'ordonnée
    </span>$<span class="kw">this-&gt;</span>y0 <span class="kw">= </span>$<span class="kw">this-&gt;</span>GetY<span class="kw">();
}

function </span>CorpsChapitre<span class="kw">(</span>$fichier<span class="kw">)
{
    </span><span class="cmt">// Lecture du fichier texte
    </span>$txt <span class="kw">= </span>file_get_contents<span class="kw">(</span>$fichier<span class="kw">);
    </span><span class="cmt">// Police
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">'Times'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>12<span class="kw">);
    </span><span class="cmt">// Sortie du texte sur 6 cm de largeur
    </span>$<span class="kw">this-&gt;</span>MultiCell<span class="kw">(</span>60<span class="kw">,</span>5<span class="kw">,</span>$txt<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Ln<span class="kw">();
    </span><span class="cmt">// Mention
    </span>$<span class="kw">this-&gt;</span>SetFont<span class="kw">(</span><span class="str">''</span><span class="kw">,</span><span class="str">'I'</span><span class="kw">);
    </span>$<span class="kw">this-&gt;</span>Cell<span class="kw">(</span>0<span class="kw">,</span>5<span class="kw">,</span><span class="str">"(fin de l'extrait)"</span><span class="kw">);
    </span><span class="cmt">// Retour en première colonne
    </span>$<span class="kw">this-&gt;</span>SetCol<span class="kw">(</span>0<span class="kw">);
}

function </span>AjouterChapitre<span class="kw">(</span>$num<span class="kw">, </span>$titre<span class="kw">, </span>$fichier<span class="kw">)
{
    </span><span class="cmt">// Ajout du chapitre
    </span>$<span class="kw">this-&gt;</span>AddPage<span class="kw">();
    </span>$<span class="kw">this-&gt;</span>TitreChapitre<span class="kw">(</span>$num<span class="kw">,</span>$titre<span class="kw">);
    </span>$<span class="kw">this-&gt;</span>CorpsChapitre<span class="kw">(</span>$fichier<span class="kw">);
}
}

</span>$pdf <span class="kw">= new </span>PDF<span class="kw">();
</span>$titre <span class="kw">= </span><span class="str">'Vingt mille lieues sous les mers'</span><span class="kw">;
</span>$pdf<span class="kw">-&gt;</span>SetTitle<span class="kw">(</span>$titre<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>SetAuthor<span class="kw">(</span><span class="str">'Jules Verne'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AjouterChapitre<span class="kw">(</span>1<span class="kw">,</span><span class="str">'UN ÉCUEIL FUYANT'</span><span class="kw">,</span><span class="str">'20k_c1.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AjouterChapitre<span class="kw">(</span>2<span class="kw">,</span><span class="str">'LE POUR ET LE CONTRE'</span><span class="kw">,</span><span class="str">'20k_c2.txt'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto4.php' target='_blank' class='demo'>[Exécuter]</a></p>
La méthode-clé utilisée est <a href='../doc/acceptpagebreak.htm'>AcceptPageBreak()</a>. Elle permet d'accepter ou non un saut de
page automatique. En refusant le saut et en modifiant la marge gauche (par <a href='../doc/setleftmargin.htm'>SetLeftMargin()</a>),
on obtient le formatage en colonnes.
<br>
Pour le reste, peu de changements ; deux propriétés ont été ajoutées à la classe pour mémoriser
le numéro de colonne courant ainsi que l'ordonnée du début des colonnes, et l'appel à MultiCell()
spécifie que le texte a 6 cm de largeur.
</body>
</html>
