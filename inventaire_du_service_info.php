<?php
require_once('config/database.php');

// Charger l'inventaire depuis la base de données
function loadInventaire() {
    global $conn;
    $stmt = $conn->query("SELECT * FROM inventaire ORDER BY id");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Ajouter un nouvel article à l'inventaire
function addInventaire($nom_article, $type, $quantite, $description, $etat) {
    global $conn;
    $stmt = $conn->prepare("INSERT INTO inventaire (nom_article, type, quantite, description, etat) VALUES (?, ?, ?, ?, ?)");
    return $stmt->execute([$nom_article, $type, $quantite, $description, $etat]);
}

// Supprimer un article de l'inventaire
function deleteInventaire($id) {
    global $conn;
    $stmt = $conn->prepare("DELETE FROM inventaire WHERE id = ?");
    return $stmt->execute([$id]);
}

// Mettre à jour la quantité d'un article
function updateQuantite($id, $quantite) {
    global $conn;
    $stmt = $conn->prepare("UPDATE inventaire SET quantite = ? WHERE id = ?");
    return $stmt->execute([$quantite, $id]);
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['ajouter'])) {
        $nom_article = $_POST['nom_article'];
        $type = $_POST['type'];
        $quantite = $_POST['quantite'];
        $description = $_POST['description'];
        $etat = $_POST['etat'];

        addInventaire($nom_article, $type, $quantite, $description, $etat);
    } elseif (isset($_POST['supprimer'])) {
        $id = $_POST['id_supprimer'];
        deleteInventaire($id);
    } elseif (isset($_POST['modifier_quantite'])) {
        $id = $_POST['id_modifier'];
        $quantite = $_POST['quantite_modifier'];
        updateQuantite($id, $quantite);
    }
}

$inventaire = loadInventaire();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventaire du Service Informatique</title>
    <style>
        /* Global styles */
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 0;
        }

        h1, h2 {
            color: #333;
            font-weight: bold;
        }

        h1 {
            text-align: center;
            margin-top: 50px;
            font-size: 2em;
        }

        h2 {
            margin-top: 30px;
            font-size: 1.5em;
        }

        /* Layout container */
        .container {
            width: 80%;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }

        /* Form styles */
        form {
            display: flex;
            flex-direction: column;
            margin-bottom: 30px;
        }

        label {
            font-size: 1em;
            margin: 8px 0;
        }

        input[type="text"], input[type="number"] {
            padding: 10px;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 12px 20px;
            background-color: #0066cc;
            color: #fff;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1em;
        }

        button:hover {
            background-color: #005bb5;
        }

        /* Table styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 10px;
            text-align: left;
            border: 1px solid #ddd;
        }

        th {
            background-color: #0066cc;
            color: white;
        }

        /* Table row styles */
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tr:hover {
            background-color: #e9f5ff;
        }

        /* Form layout */
        .form-container {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }

        .form-container form {
            width: 48%;
        }
    </style>
</head>
<body>
<?php include 'top_bar.php'; ?>
<div class="container">
    <h1>Inventaire du Service Informatique</h1>

    <!-- Formulaire pour ajouter du matériel -->
    <div class="form-container">
        <form method="POST">
            <h2>Ajouter un Matériel</h2>
            <label for="nom_article">Nom de l'article :</label>
            <input type="text" id="nom_article" name="nom_article" required>

            <label for="type">Type :</label>
            <input type="text" id="type" name="type" required>

            <label for="quantite">Quantité :</label>
            <input type="number" id="quantite" name="quantite" required>

            <label for="description">Description :</label>
            <input type="text" id="description" name="description" required>

            <label for="etat">État :</label>
            <input type="text" id="etat" name="etat" required>

            <button type="submit" name="ajouter">Ajouter</button>
        </form>

        <form method="POST">
            <h2>Supprimer un Matériel</h2>
            <label for="id_supprimer">ID de l'article à supprimer :</label>
            <input type="number" id="id_supprimer" name="id_supprimer" required>

            <button type="submit" name="supprimer">Supprimer</button>
        </form>

        <form method="POST">
            <h2>Modifier la Quantité</h2>
            <label for="id_modifier">ID de l'article :</label>
            <input type="number" id="id_modifier" name="id_modifier" required>

            <label for="quantite_modifier">Quantité :</label>
            <input type="number" id="quantite_modifier" name="quantite_modifier" required>

            <button type="submit" name="modifier_quantite">Modifier Quantité</button>
        </form>
    </div>
    <h2>Liste de l'Inventaire</h2>
    <table>
        <tr>
            <th>ID</th>
            <th>Nom de l'article</th>
            <th>Type</th>
            <th>Quantité</th>
            <th>Description</th>
            <th>État</th>
        </tr>
        <?php foreach ($inventaire as $row) { ?>
            <tr>
                <td><?php echo $row['id']; ?></td>
                <td><?php echo $row['nom_article']; ?></td>
                <td><?php echo $row['type']; ?></td>
                <td><?php echo $row['quantite']; ?></td>
                <td><?php echo $row['description']; ?></td>
                <td><?php echo $row['etat']; ?></td>
            </tr>
        <?php } ?>
    </table>
</div>
</body>
</html>
