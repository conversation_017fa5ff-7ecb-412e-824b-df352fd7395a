const translations = {
    fr: {
        "title": "Demande de Ticket d'Assistance",
        "form-title": "Veuillez remplir le formulaire ci-dessous pour soumettre une demande de ticket d'assistance.",
        // ...other translations...
    },
    en: {
        "title": "Support Ticket Request",
        "form-title": "Please fill out the form below to submit a support ticket request.",
        // ...other translations...
    },
    de: {
        "title": "Support-Ticket-Anfrage",
        "form-title": "Bitte füllen Sie das untenstehende Formular aus, um eine Support-Ticket-Anfrage einzureichen.",
        // ...other translations...
    }
};

function changeLanguage(lang) {
    localStorage.setItem('language', lang);
    document.querySelectorAll('[data-section]').forEach(element => {
        const section = element.getAttribute('data-section');
        if (translations[lang][section]) {
            element.textContent = translations[lang][section];
        }
    });
}

document.addEventListener('DOMContentLoaded', () => {
    const lang = localStorage.getItem('language') || 'fr';
    changeLanguage(lang);
});
