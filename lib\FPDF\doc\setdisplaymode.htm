<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetDisplayMode</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetDisplayMode</h1>
<code>SetDisplayMode(<b>mixed</b> zoom [, <b>string</b> layout])</code>
<h2>Description</h2>
Contrôle la manière dont le document sera affiché par le lecteur. On peut régler le niveau de
zoom : afficher la page en entier, occuper toute la largeur de la fenêtre, utiliser la taille
réelle, choisir un facteur de zoom particulier ou encore utiliser la valeur par défaut de
l'utilisateur (celle paramétrée dans le menu Préférences d'Adobe Reader). On peut également
spécifier la disposition des pages : une seule à la fois, affichage continu, pages sur deux
colonnes ou réglage par défaut.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>zoom</code></dt>
<dd>
Le zoom à utiliser. Il peut prendre l'une des valeurs chaînes suivantes :
<ul>
<li><code>fullpage</code> : affiche entièrement les pages à l'écran</li>
<li><code>fullwidth</code> : maximise la largeur des pages</li>
<li><code>real</code> : affiche la taille réelle (équivaut à un zoom de 100%)</li>
<li><code>default</code> : utilise le mode d'affichage par défaut du lecteur</li>
</ul>
ou bien un entier indiquant le facteur de zoom à utiliser.
</dd>
<dt><code>layout</code></dt>
<dd>
La disposition des pages. Les valeurs possibles sont :
<ul>
<li><code>single</code> : affiche une seule page à la fois</li>
<li><code>continuous</code> : affichage continu d'une page à l'autre</li>
<li><code>two</code> : affiche deux pages sur deux colonnes</li>
<li><code>default</code> : utilise le mode d'affichage par défaut du lecteur</li>
</ul>
La valeur par défaut est <code>default</code>.
</dd>
</dl>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
