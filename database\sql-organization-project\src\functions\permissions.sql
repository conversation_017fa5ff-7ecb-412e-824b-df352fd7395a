-- Function to check if a user has a specific permission
CREATE FUNCTION has_permission(user_id INT, permission_name VARCHAR(50))
RETURNS BOOLEAN
BEGIN
    DECLARE role_id INT;
    DECLARE department_id INT;
    
    -- Get the user's role and department
    SELECT role_id, department_id INTO role_id, department_id
    FROM users
    WHERE id = user_id;

    -- Check if the role has the permission
    IF EXISTS (SELECT 1 FROM roles WHERE id = role_id AND name = permission_name) THEN
        RETURN TRUE;
    END IF;

    -- Check if the department has the permission
    IF EXISTS (SELECT 1 FROM departments WHERE id = department_id AND name = permission_name) THEN
        RETURN TRUE;
    END IF;

    RETURN FALSE;
END;

-- Function to grant a permission to a user
CREATE FUNCTION grant_permission(user_id INT, permission_name VARCHAR(50))
RETURNS BOOLEAN
BEGIN
    -- Logic to grant permission (this is a placeholder for actual implementation)
    -- This could involve inserting a record into a permissions table
    RETURN TRUE;
END;

-- Function to revoke a permission from a user
CREATE FUNCTION revoke_permission(user_id INT, permission_name VARCHAR(50))
RETURNS BOOLEAN
BEGIN
    -- Logic to revoke permission (this is a placeholder for actual implementation)
    -- This could involve deleting a record from a permissions table
    RETURN TRUE;
END;