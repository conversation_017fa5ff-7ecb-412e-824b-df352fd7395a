# SQL Organization Project

## Overview
This project is designed to manage user creation and permissions based on a hierarchical and functional organizational chart. It includes the necessary SQL scripts to create tables, populate them with initial data, and manage user permissions.

## Project Structure
```
sql-organization-project
├── src
│   ├── schema
│   │   ├── tables.sql        # SQL statements for creating tables
│   │   └── indexes.sql       # SQL statements for creating indexes
│   ├── data
│   │   ├── departments.sql    # SQL insert statements for departments
│   │   ├── roles.sql         # SQL insert statements for roles
│   │   └── users.sql         # SQL insert statements for users
│   ├── functions
│   │   └── permissions.sql    # Functions for managing user permissions
│   └── procedures
│       └── user_management.sql # Procedures for managing user accounts
├── migrations
│   └── initial.sql            # Initial migration setup
├── tests
│   └── test_permissions.sql    # Tests for permissions functions
└── README.md                  # Project documentation
```

## Setup Instructions
1. **Database Setup**: Ensure you have a SQL database set up and accessible.
2. **Run Migrations**: Execute the `migrations/initial.sql` script to create the necessary tables and populate them with initial data.
3. **Insert Data**: Run the scripts in the `src/data` directory to populate the tables with predefined data.
4. **Manage Users**: Use the stored procedures in `src/procedures/user_management.sql` to manage user accounts.

## Usage Guidelines
- Use the functions defined in `src/functions/permissions.sql` to manage user permissions based on their roles and department affiliations.
- Test the functionality using the scripts in the `tests` directory to ensure everything works as expected.

## License
This project is licensed under the MIT License. See the LICENSE file for more details.