<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Ln</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Ln</h1>
<code>Ln([<b>float</b> h])</code>
<h2>Description</h2>
Effectue un saut de ligne. L'abscisse courante est ramenée à la valeur de la marge gauche et
l'ordonnée augmente de la valeur indiquée en paramètre.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>h</code></dt>
<dd>
L'amplitude du saut de ligne.
<br>
Par défaut, la valeur est égale à la hauteur de la dernière cellule imprimée.
</dd>
</dl>
<h2>Voir</h2>
<a href="cell.htm">Cell</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
