/* Search Container */
.search-container {
    margin-bottom: 20px;
    width: 100%;
}

.search-container input {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-container input:focus {
    border-color: var(--primary-color);
    outline: none;
}

/* Card animations and highlights */
.provider-card, .service-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.provider-card:hover, .service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.highlight-match {
    animation: highlightFade 1s ease;
}

@keyframes highlightFade {
    0% { background-color: rgba(var(--primary-color-rgb), 0.1); }
    100% { background-color: transparent; }
}

/* Loading states and feedback */
.loading {
    position: relative;
    pointer-events: none;
    opacity: 0.7;
}

.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    border-right-color: transparent;
    animation: rotate 0.8s infinite linear;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Improved form inputs */
.form-control {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* Rating group enhancements */
.rating-group {
    display: flex;
    gap: 10px;
    margin: 10px 0;
}

.rating-option {
    position: relative;
}

.rating-option input[type="radio"] {
    position: absolute;
    opacity: 0;
}

.rating-option label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rating-option input[type="radio"]:checked + label {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.rating-option input[type="radio"]:focus + label {
    box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

/* Responsive enhancements */
@media (max-width: 768px) {
    .rating-group {
        flex-wrap: wrap;
        justify-content: center;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .evaluation-header h1 {
        font-size: 1.5rem;
    }

    .provider-card, .service-card {
        padding: 15px;
    }
}

/* Accessibility improvements */
:focus-visible {
    outline: 3px solid var(--primary-color);
    outline-offset: 2px;
}
