# Sécurisation du dossier uploads/pdf
# Empêche l'exécution de scripts PHP dans ce dossier

# Désactiver l'exécution de PHP
php_flag engine off

# Autoriser seulement les fichiers PDF
<FilesMatch "\.(pdf)$">
    Order Allow,<PERSON><PERSON>ow from all
</FilesMatch>

# Bloquer tous les autres types de fichiers
<FilesMatch "\.(?!pdf$)[^.]+$">
    Order Allow,Deny
    Den<PERSON> from all
</FilesMatch>

# Empêcher l'affichage du contenu du dossier
Options -Indexes

# Headers de sécurité pour les PDF
<FilesMatch "\.pdf$">
    Header set Content-Type "application/pdf"
    Header set Content-Disposition "inline"
    Header set X-Content-Type-Options "nosniff"
</FilesMatch>
