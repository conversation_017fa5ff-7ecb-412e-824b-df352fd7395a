-- Insertion des départements principaux
INSERT INTO departments (name, color_code) VALUES
('Direction Générale', '#808080'),          -- <PERSON><PERSON>
('Direction Commerciale', '#FF0000'),       -- <PERSON>
('Direction Opérations', '#FFFF00'),        -- <PERSON><PERSON><PERSON>
('Direction Support', '#0000FF');           -- Bleu

-- Ajout des sous-départements par section
INSERT INTO departments (name, parent_id) VALUES
-- Section Direction Générale (Gris)
('DAF', (SELECT id FROM departments WHERE name = 'Direction Générale')),
('DRH', (SELECT id FROM departments WHERE name = 'Direction Générale')),
('DSI', (SELECT id FROM departments WHERE name = 'Direction Générale')),

-- Section Direction Commerciale (Rouge)
('Ventes Externes', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Ventes Internes', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Marketing', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Communication', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),

-- Section Direction Opérations (Jaune)
('Production', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Service Technique', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('R&D', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Logistique', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Services Généraux', (SELECT id FROM departments WHERE name = 'Direction Opérations')),

-- Sous-départements de Ventes Externes
('Prescription', (SELECT id FROM departments WHERE name = 'Ventes Externes')),
('Commercial Terrain', (SELECT id FROM departments WHERE name = 'Ventes Externes')),

-- Sous-départements de Ventes Internes
('ADV', (SELECT id FROM departments WHERE name = 'Ventes Internes')),
('Support Commercial', (SELECT id FROM departments WHERE name = 'Ventes Internes')),

-- Sous-départements de Marketing
('Graphisme', (SELECT id FROM departments WHERE name = 'Marketing')),
('Chef Produit', (SELECT id FROM departments WHERE name = 'Marketing')),

-- Sous-départements de Service Technique
('SAV', (SELECT id FROM departments WHERE name = 'Service Technique')),
('Formation', (SELECT id FROM departments WHERE name = 'Service Technique')),
('Assistance Technique', (SELECT id FROM departments WHERE name = 'Service Technique')),

-- Sous-départements de Logistique
('Reception', (SELECT id FROM departments WHERE name = 'Logistique')),
('Expédition', (SELECT id FROM departments WHERE name = 'Logistique')),
('Stock', (SELECT id FROM departments WHERE name = 'Logistique'));