<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Error</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Error</h1>
<code>Error(<b>string</b> msg)</code>
<h2>Description</h2>
Cette méthode est appelée automatiquement en cas d'erreur fatale ; elle se contente de lancer
une exception avec le message fourni.<br>
Une classe dérivée peut redéfinir la méthode afin de personnaliser le traitement des erreurs,
mais elle doit impérativement interrompre le traitement, sinon le fichier PDF résultant serait
probablement invalide.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>msg</code></dt>
<dd>
Le message d'erreur.
</dd>
</dl>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
