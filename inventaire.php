<?php include 'top-bar-informatique.php';

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function loadInventaire() {
    $conn = getDbConnection();
    $stmt = $conn->query("SELECT * FROM inventaire ORDER BY created_at DESC");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function addInventaire($nom_article, $type, $quantite, $description, $etat) {
    $conn = getDbConnection();
    $sql = "INSERT INTO inventaire (nom_article, type, quantite, description, etat) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$nom_article, $type, $quantite, $description, $etat]);
}

function deleteInventaire($id) {
    $conn = getDbConnection();
    $sql = "DELETE FROM inventaire WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
}

function updateQuantite($id, $quantite) {
    $conn = getDbConnection();
    $sql = "UPDATE inventaire SET quantite = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$quantite, $id]);
}

$inventaire = loadInventaire();

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    if (isset($_POST['ajouter'])) {
        addInventaire($_POST['nom_article'], $_POST['type'], $_POST['quantite'], $_POST['description'], $_POST['etat']);
    } elseif (isset($_POST['supprimer'])) {
        deleteInventaire($_POST['id_supprimer']);
    } elseif (isset($_POST['modifier_quantite'])) {
        updateQuantite($_POST['id_modifier'], $_POST['quantite_modifier']);
    }
    $inventaire = loadInventaire(); // Reload updated data
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Inventaire du Service Informatique</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
<div class="container">
    <h1 class="page-title">Inventaire du Service Informatique</h1>

    <!-- Redirection Button -->
    <div style="text-align: right; margin-bottom: 20px;">
        <a href="historique_inventaire.php" class="btn-primary" style="text-decoration: none; padding: 10px 20px; background-color: #f57c00; color: white; border-radius: 4px;">Voir l'Historique</a>
    </div>

    <!-- Form Section -->
    <div class="form-section">
        <div class="form-card">
            <h2>Ajouter un Matériel</h2>
            <form method="POST">
                <label for="nom_article">Nom de l'article :</label>
                <input type="text" id="nom_article" name="nom_article" required>

                <label for="type">Type :</label>
                <input type="text" id="type" name="type" required>

                <label for="quantite">Quantité :</label>
                <input type="number" id="quantite" name="quantite" required>

                <label for="description">Description :</label>
                <input type="text" id="description" name="description" required>

                <label for="etat">État :</label>
                <input type="text" id="etat" name="etat" required>

                <button type="submit" name="ajouter" class="btn-primary">Ajouter</button>
            </form>
        </div>

        <div class="form-card">
            <h2>Supprimer un Matériel</h2>
            <form method="POST">
                <label for="id_supprimer">ID de l'article à supprimer :</label>
                <input type="number" id="id_supprimer" name="id_supprimer" required>

                <button type="submit" name="supprimer" class="btn-danger">Supprimer</button>
            </form>
        </div>

        <div class="form-card">
            <h2>Modifier la Quantité</h2>
            <form method="POST">
                <label for="id_modifier">ID de l'article :</label>
                <input type="number" id="id_modifier" name="id_modifier" required>

                <label for="quantite_modifier">Quantité :</label>
                <input type="number" id="quantite_modifier" name="quantite_modifier" required>

                <button type="submit" name="modifier_quantite" class="btn-primary">Modifier Quantité</button>
            </form>
        </div>
    </div>

    <!-- Inventory Table -->
    <h2 class="section-title">Liste de l'Inventaire</h2>
    <div class="table-container">
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nom de l'article</th>
                    <th>Type</th>
                    <th>Quantité</th>
                    <th>Description</th>
                    <th>État</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($inventaire as $row): ?>
                    <tr>
                        <td><?= htmlspecialchars($row['id']) ?></td>
                        <td><?= htmlspecialchars($row['nom_article']) ?></td>
                        <td><?= htmlspecialchars($row['type']) ?></td>
                        <td><?= htmlspecialchars($row['quantite']) ?></td>
                        <td><?= htmlspecialchars($row['description']) ?></td>
                        <td><?= htmlspecialchars($row['etat']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<?php include 'bottom_bar.php'; ?>
</body>
</html>
