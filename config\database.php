<?php
define('DB_HOST', 'localhost');
define('DB_NAME', 'schluter_db');
define('DB_USER', 'root');
define('DB_PASS', '');

try {
    $db = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
} catch(PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}

// Vérifier si la base de données existe, sinon la créer
try {
    $tmpDb = new PDO("mysql:host=" . DB_HOST, DB_USER, DB_PASS);
    $tmpDb->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME);
    
    // Se connecter à la base de données créée
    $db = new PDO(
        "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8",
        DB_USER,
        DB_PASS,
        [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
    );
} catch(PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}
