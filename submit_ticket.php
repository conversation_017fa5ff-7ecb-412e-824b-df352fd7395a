<?php
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require 'vendor/autoload.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $titre = htmlspecialchars($_POST['titre']);
    $texte = htmlspecialchars($_POST['texte']);

    // Vérification des champs obligatoires
    if (empty($titre) || empty($texte)) {
        echo "Veuillez remplir tous les champs obligatoires.";
        exit;
    }

    $to = "<EMAIL>, <EMAIL>";
    $subject = "Nouveau Ticket d'Assistance: " . $titre;
    $message = "Titre: " . $titre . "\n\nTexte: " . $texte;

    // Vérification du fichier uploadé
    if (isset($_FILES['capture']) && $_FILES['capture']['error'] == UPLOAD_ERR_OK) {
        $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
        if (!in_array($_FILES['capture']['type'], $allowedTypes)) {
            echo "Format de fichier non autorisé.";
            exit;
        }
        if ($_FILES['capture']['size'] > 2 * 1024 * 1024) { // Limite de 2 Mo
            echo "Le fichier est trop volumineux.";
            exit;
        }

        $tmp_name = $_FILES['capture']['tmp_name'];
        $file_name = $_FILES['capture']['name'];
        $file_path = "uploads/" . basename($file_name);

        // Déplace le fichier téléchargé vers le répertoire souhaité
        if (move_uploaded_file($tmp_name, $file_path)) {
            $message .= "\n\nCapture d'écran jointe: " . $file_path;
        }
    }

    $mail = new PHPMailer(true);
    try {
        //Server settings
        $mail->isSMTP();
        $mail->Host = 'smtp.votre-entreprise.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>';
        $mail->Password = 'votre_mot_de_passe';
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;

        //Recipients
        $mail->setFrom('<EMAIL>', 'Votre Entreprise');
        $mail->addAddress('<EMAIL>');
        $mail->addAddress('<EMAIL>');

        //Content
        $mail->isHTML(false);
        $mail->Subject = $subject;
        $mail->Body    = $message;

        if (isset($file_path)) {
            $mail->addAttachment($file_path);
        }

        $mail->send();
        echo "<script>alert('Votre ticket a été envoyé avec succès.'); window.location.href='assistance.php';</script>";
    } catch (Exception $e) {
        echo "<script>alert('Erreur lors de l\'envoi du ticket: {$mail->ErrorInfo}'); window.history.back();</script>";
    }
}
?>
