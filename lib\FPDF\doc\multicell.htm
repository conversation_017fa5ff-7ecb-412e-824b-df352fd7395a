<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>MultiCell</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>MultiCell</h1>
<code>MultiCell(<b>float</b> w, <b>float</b> h, <b>string</b> txt [, <b>mixed</b> border [, <b>string</b> align [, <b>boolean</b> fill]]])</code>
<h2>Description</h2>
Cette méthode permet d'imprimer du texte avec des retours à la ligne. Ceux-ci peuvent être
automatiques (dès que le texte atteint le bord droit de la cellule) ou explicites (via le
caractère \n). Autant de cellules que nécessaire sont imprimées, les unes en dessous des
autres.
<br>
Le texte peut être aligné, centré ou justifié. Le bloc de cellules peut être encadré et le
fond coloré.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>w</code></dt>
<dd>
Largeur des cellules. Si elle vaut <code>0</code>, elles s'étendent jusqu'à la marge droite de
la page.
</dd>
<dt><code>h</code></dt>
<dd>
Hauteur des cellules.
</dd>
<dt><code>txt</code></dt>
<dd>
Chaîne à imprimer.
</dd>
<dt><code>border</code></dt>
<dd>
Indique si des bords doivent être tracés autour du bloc de cellules. La valeur peut être soit un
nombre :
<ul>
<li><code>0</code> : aucun bord</li>
<li><code>1</code> : cadre</li>
</ul>
soit une chaîne contenant certains ou tous les caractères suivants (dans un ordre quelconque) :
<ul>
<li><code>L</code> : gauche</li>
<li><code>T</code> : haut</li>
<li><code>R</code> : droit</li>
<li><code>B</code> : bas</li>
</ul>
La valeur par défaut est <code>0</code>.
</dd>
<dt><code>align</code></dt>
<dd>
Contrôle l'alignement du texte. Les valeurs possibles sont :
<ul>
<li><code>L</code> : alignement à gauche</li>
<li><code>C</code> : centrage</li>
<li><code>R</code> : alignement à droite</li>
<li><code>J</code> : justification (valeur par défaut)</li>
</ul>
</dd>
<dt><code>fill</code></dt>
<dd>
Indique si le fond des cellules doit être coloré (<code>true</code>) ou transparent (<code>false</code>).
Valeur par défaut : <code>false</code>.
</dd>
</dl>
<h2>Voir</h2>
<a href="setfont.htm">SetFont</a>,
<a href="setdrawcolor.htm">SetDrawColor</a>,
<a href="setfillcolor.htm">SetFillColor</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="setlinewidth.htm">SetLineWidth</a>,
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
