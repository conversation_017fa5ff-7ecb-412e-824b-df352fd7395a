<?php
require_once('config/database.php');

function migrateDataToDB($csvFile, $tableName, $columns) {    global $db;
    
    // Ajout du chemin complet
    $csvFile = __DIR__ . '/' . $csvFile;
    
    if (file_exists($csvFile) && ($handle = fopen($csvFile, "r")) !== FALSE) {
        // Ignorer l'en-tête
        fgetcsv($handle);
        
        $placeholders = str_repeat('?,', count($columns) - 1) . '?';
        $sql = "INSERT INTO $tableName (" . implode(',', $columns) . ") VALUES ($placeholders)";
        
        try {
            // Vider la table avant d'insérer les nouvelles données
            $db->exec("TRUNCATE TABLE $tableName");
            
            $stmt = $db->prepare($sql);
            
            while (($data = fgetcsv($handle)) !== FALSE) {
                if (count($data) >= count($columns)) {
                    $stmt->execute($data);
                }
            }
            echo "Migration réussie pour $tableName<br>";
            
            // Supprimer le fichier CSV
            fclose($handle);
            unlink($csvFile);
            echo "Fichier $csvFile supprimé<br>";
        } catch(PDOException $e) {
            echo "Erreur lors de la migration de $tableName : " . $e->getMessage() . "<br>";
        }
    } else {
        echo "Fichier $csvFile non trouvé<br>";
    }
}

try {
    // Copier d'abord les fichiers CSV depuis le dossier chris vers le dossier sauvé
    $sourceDir = __DIR__ . '/../chris/';
    $csvFiles = ['accuses_reception.csv', 'inventaire.csv', 'pc_installes.csv', 'pc_prevus.csv', 'pc_a_prevoir.csv'];
    
    foreach ($csvFiles as $file) {
        if (file_exists($sourceDir . $file)) {
            copy($sourceDir . $file, __DIR__ . '/' . $file);
            echo "Fichier $file copié depuis le dossier chris<br>";
        }
    }

    // Migrer les accusés de réception
    migrateDataToDB(
        "accuses_reception.csv",
        "accuses_reception",
        ['collaborateur', 'responsable', 'date_creation', 'materiel_remis', 'materiel_recu']
    );    // Migrer les évaluations
    migrateDataToDB(
        "evaluations.csv",
        "evaluations",
        ['prestataire', 'service', 'qualite', 'delai', 'communication', 'commentaires']
    );    // Migrer les changements de poste
    migrateDataToDB(
        "changements_de_poste.csv",
        "changements_poste",
        ['nom_utilisateur', 'ancien_poste', 'nouveau_poste', 'date_changement', 'lifecycle', 'responsable', 'type']
    );    // Migrer l'inventaire
    migrateDataToDB(
        "inventaire.csv",
        "inventaire",
        ['nom_article', 'type', 'quantite', 'description', 'etat']
    );

    // Migrer les PC installés, prévus et à prévoir
    foreach(['pc_installes.csv', 'pc_prevus.csv', 'pc_a_prevoir.csv'] as $csvFile) {
        $type = str_replace('.csv', '', $csvFile);        migrateDataToDB(
            $csvFile,
            "changements_poste",
            ['nom_utilisateur', 'ancien_poste', 'nouveau_poste', 'date_changement', 'lifecycle', 'responsable']
        );
    }    // Migrer les services
    migrateDataToDB(
        "services.csv",
        "services",
        ['title', 'description', 'icon']
    );    // Migrer les événements
    migrateDataToDB(
        "evenements.csv",
        "evenements",
        ['titre', 'description', 'date_debut', 'date_fin']
    );

    echo "Migration complète terminée avec succès!";
} catch (Exception $e) {
    echo "Erreur générale : " . $e->getMessage();
}
?>
