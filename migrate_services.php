<?php
// Script de migration des services depuis le CSV vers la base de données
$db = new PDO('mysql:host=localhost;dbname=schluter_db', 'root', '', array(
    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
));

// Vider la table services avant la migration
$db->exec('TRUNCATE TABLE services');

// Lire le fichier CSV et insérer les données dans la base de données
$filename = 'services.csv';
if (file_exists($filename) && ($handle = fopen($filename, 'r')) !== false) {
    $stmt = $db->prepare('INSERT INTO services (title, description, icon) VALUES (?, ?, ?)');
    
    while (($data = fgetcsv($handle, 1000, ',')) !== false) {
        try {
            $stmt->execute($data);
            echo "Service ajouté : " . $data[0] . "\n";
        } catch (PDOException $e) {
            echo "Erreur lors de l'ajout du service " . $data[0] . ": " . $e->getMessage() . "\n";
        }
    }
    fclose($handle);
    
    echo "\nMigration terminée avec succès!";
} else {
    echo "Erreur: Le fichier services.csv n'a pas été trouvé.";
}
?>
