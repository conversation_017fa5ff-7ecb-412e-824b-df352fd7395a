<?php
require_once __DIR__ . '/config/database.php';

try {
    // Migration des prestataires existants
    $existingPrestataires = $db->query("SELECT DISTINCT prestataire FROM evaluations")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($existingPrestataires as $prestataire) {
        if (!empty($prestataire)) {
            $db->prepare("INSERT IGNORE INTO prestataires (nom) VALUES (?)")->execute([$prestataire]);
        }
    }
    
    // Migration des services existants
    $existingServices = $db->query("SELECT DISTINCT service FROM evaluations")->fetchAll(PDO::FETCH_COLUMN);
    foreach ($existingServices as $service) {
        if (!empty($service)) {
            $db->prepare("INSERT IGNORE INTO services (nom) VALUES (?)")->execute([$service]);
        }
    }
    
    echo "Migration des données terminée avec succès!";
} catch (PDOException $e) {
    die("Erreur lors de la migration: " . $e->getMessage());
}
