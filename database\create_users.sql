-- Structure de base pour la gestion des utilisateurs
-- Création de la table des départements
CREATE TABLE IF NOT EXISTS departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    color_code VARCHAR(7),
    parent_id INT,
    FOREIGN KEY (parent_id) REFERENCES departments(id)
);

-- Création de la table des rôles
CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    description TEXT
);

-- Création de la table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    first_name VARCHAR(50),
    last_name VA<PERSON><PERSON><PERSON>(50),
    department_id INT,
    role_id INT,
    manager_id INT,
    is_first_login <PERSON><PERSON><PERSON><PERSON>N DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    FOREIGN KEY (department_id) REFERENCES departments(id),
    FOREIGN KEY (role_id) REFERENCES roles(id),
    FOREIGN KEY (manager_id) REFERENCES users(id)
);

-- Insertion des rôles de base
INSERT INTO roles (name, description) VALUES
('super_admin', 'Administrateur système avec tous les droits'),
('admin', 'Administrateur départemental'),
('manager', 'Manager d''équipe'),
('user', 'Utilisateur standard'),
('direction', 'Direction'),
('responsable', 'Responsable de service');

-- Suppression des anciennes données si elles existent
DELETE FROM departments;

-- Réinitialisation de l'auto-increment
ALTER TABLE departments AUTO_INCREMENT = 1;

-- Insertion des départements principaux
INSERT INTO departments (name, color_code) VALUES
('Direction Générale', '#808080'),          -- Gris
('Direction Commerciale', '#FF0000'),       -- Rouge
('Direction Opérations', '#FFFF00'),        -- Jaune
('Direction Support', '#0000FF');           -- Bleu

-- Ajout des sous-départements par section
INSERT INTO departments (name, parent_id) VALUES
-- Section Direction Générale (Gris)
('DAF', (SELECT id FROM departments WHERE name = 'Direction Générale')),
('DRH', (SELECT id FROM departments WHERE name = 'Direction Générale')),
('DSI', (SELECT id FROM departments WHERE name = 'Direction Générale')),

-- Section Direction Commerciale (Rouge)
('Ventes Externes', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Ventes Internes', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Marketing', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),
('Communication', (SELECT id FROM departments WHERE name = 'Direction Commerciale')),

-- Section Direction Opérations (Jaune)
('Production', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Service Technique', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('R&D', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Logistique', (SELECT id FROM departments WHERE name = 'Direction Opérations')),
('Services Généraux', (SELECT id FROM departments WHERE name = 'Direction Opérations')),

-- Sous-départements de Ventes Externes
('Prescription', (SELECT id FROM departments WHERE name = 'Ventes Externes')),
('Commercial Terrain', (SELECT id FROM departments WHERE name = 'Ventes Externes')),

-- Sous-départements de Ventes Internes
('ADV', (SELECT id FROM departments WHERE name = 'Ventes Internes')),
('Support Commercial', (SELECT id FROM departments WHERE name = 'Ventes Internes')),

-- Sous-départements de Marketing
('Graphisme', (SELECT id FROM departments WHERE name = 'Marketing')),
('Chef Produit', (SELECT id FROM departments WHERE name = 'Marketing')),

-- Sous-départements de Service Technique
('SAV', (SELECT id FROM departments WHERE name = 'Service Technique')),
('Formation', (SELECT id FROM departments WHERE name = 'Service Technique')),
('Assistance Technique', (SELECT id FROM departments WHERE name = 'Service Technique')),

-- Sous-départements de Logistique
('Reception', (SELECT id FROM departments WHERE name = 'Logistique')),
('Expédition', (SELECT id FROM departments WHERE name = 'Logistique')),
('Stock', (SELECT id FROM departments WHERE name = 'Logistique'));

-- Création de la table pour les liens fonctionnels
CREATE TABLE IF NOT EXISTS functional_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_department_id INT,
    to_department_id INT,
    link_type VARCHAR(50),
    FOREIGN KEY (from_department_id) REFERENCES departments(id),
    FOREIGN KEY (to_department_id) REFERENCES departments(id)
);

-- Insertion des liens fonctionnels (lignes pointillées de l'organigramme)
INSERT INTO functional_links (from_department_id, to_department_id, link_type) VALUES
-- Liens depuis Service Technique
((SELECT id FROM departments WHERE name = 'Service Technique'),
 (SELECT id FROM departments WHERE name = 'ADV'),
 'support_technique'),

((SELECT id FROM departments WHERE name = 'Service Technique'),
 (SELECT id FROM departments WHERE name = 'Commercial Terrain'),
 'support_technique'),

((SELECT id FROM departments WHERE name = 'Service Technique'),
 (SELECT id FROM departments WHERE name = 'Prescription'),
 'support_technique'),

-- Liens depuis R&D
((SELECT id FROM departments WHERE name = 'R&D'),
 (SELECT id FROM departments WHERE name = 'Service Technique'),
 'support_innovation'),

((SELECT id FROM departments WHERE name = 'R&D'),
 (SELECT id FROM departments WHERE name = 'Marketing'),
 'developpement_produit'),

-- Liens depuis Production
((SELECT id FROM departments WHERE name = 'Production'),
 (SELECT id FROM departments WHERE name = 'Logistique'),
 'flux_production'),

-- Liens depuis Support Commercial
((SELECT id FROM departments WHERE name = 'Support Commercial'),
 (SELECT id FROM departments WHERE name = 'ADV'),
 'support_ventes'),

-- Liens depuis Chef Produit
((SELECT id FROM departments WHERE name = 'Chef Produit'),
 (SELECT id FROM departments WHERE name = 'Commercial Terrain'),
 'support_produit'),

((SELECT id FROM departments WHERE name = 'Chef Produit'),
 (SELECT id FROM departments WHERE name = 'Prescription'),
 'support_produit'),

-- Liens depuis Communication
((SELECT id FROM departments WHERE name = 'Communication'),
 (SELECT id FROM departments WHERE name = 'Marketing'),
 'support_marketing'),

((SELECT id FROM departments WHERE name = 'Communication'),
 (SELECT id FROM departments WHERE name = 'Commercial Terrain'),
 'support_communication'),

-- Liens depuis Graphisme
((SELECT id FROM departments WHERE name = 'Graphisme'),
 (SELECT id FROM departments WHERE name = 'Communication'),
 'support_visuel'),

((SELECT id FROM departments WHERE name = 'Graphisme'),
 (SELECT id FROM departments WHERE name = 'Marketing'),
 'support_visuel'),

-- Liens depuis Services Généraux
((SELECT id FROM departments WHERE name = 'Services Généraux'),
 (SELECT id FROM departments WHERE name = 'Logistique'),
 'support_logistique'),

-- Liens depuis DSI
((SELECT id FROM departments WHERE name = 'DSI'),
 (SELECT id FROM departments WHERE name = 'Service Technique'),
 'support_informatique'),

((SELECT id FROM departments WHERE name = 'DSI'),
 (SELECT id FROM departments WHERE name = 'ADV'),
 'support_informatique'),

-- Liens depuis DRH
((SELECT id FROM departments WHERE name = 'DRH'),
 (SELECT id FROM departments WHERE name = 'Service Technique'),
 'support_rh'),

((SELECT id FROM departments WHERE name = 'DRH'),
 (SELECT id FROM departments WHERE name = 'Production'),
 'support_rh'),

-- Liens depuis DAF
((SELECT id FROM departments WHERE name = 'DAF'),
 (SELECT id FROM departments WHERE name = 'ADV'),
 'support_financier'),

((SELECT id FROM departments WHERE name = 'DAF'),
 (SELECT id FROM departments WHERE name = 'Logistique'),
 'support_financier');

-- Ajout d'index pour optimiser les requêtes
CREATE INDEX idx_functional_links_from ON functional_links(from_department_id);
CREATE INDEX idx_functional_links_to ON functional_links(to_department_id);
CREATE INDEX idx_departments_parent ON departments(parent_id);
CREATE INDEX idx_users_department ON users(department_id);
CREATE INDEX idx_users_role ON users(role_id);
CREATE INDEX idx_users_manager ON users(manager_id);