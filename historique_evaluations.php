<?php
require_once __DIR__ . '/config/database.php';

try {
    // <PERSON><PERSON><PERSON><PERSON><PERSON> toutes les évaluations avec tri par date
    $stmt = $db->query("SELECT * FROM evaluations ORDER BY created_at DESC");
    $evaluations = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Erreur de base de données : " . $e->getMessage());
}
?>
<?php include 'top-bar-eval.php'; ?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Historique des Évaluations</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            width: 80%;
            margin: 50px auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f57c00;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        tr:hover {
            background-color: #f1f1f1;
        }
        .top-bar {
            background-color: #333;
            color: white;
            padding: 10px;
            text-align: center;
        }
        .top-bar a {
            color: white;
            margin: 0 15px;
            text-decoration: none;
        }
        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 10px;
            position: fixed;
            width: 100%;
            bottom: 0;
        }
        .date-info {
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Historique des Évaluations</h1>
        <table>
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Prestataire</th>
                    <th>Service</th>
                    <th>Qualité</th>
                    <th>Délai</th>
                    <th>Communication</th>
                    <th>Commentaires</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($evaluations as $evaluation): ?>
                    <tr>
                        <td class="date-info"><?= date('d/m/Y H:i', strtotime($evaluation['created_at'])) ?></td>
                        <td><?= htmlspecialchars($evaluation['prestataire']) ?></td>
                        <td><?= htmlspecialchars($evaluation['service']) ?></td>
                        <td><?= htmlspecialchars($evaluation['qualite']) ?></td>
                        <td><?= htmlspecialchars($evaluation['delai']) ?></td>
                        <td><?= htmlspecialchars($evaluation['communication']) ?></td>
                        <td><?= htmlspecialchars($evaluation['commentaires']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <footer>
        <p>&copy; 2025 Schluter Systems. Tous droits réservés.</p>
        <button onclick="changeLanguage('fr')">Français</button>
        <button onclick="changeLanguage('en')">English</button>
        <button onclick="changeLanguage('de')">Deutsch</button>
        <button id="backToTop" style="display:none;" onclick="scrollToTop()">Retour en haut</button>
    </footer>
    <script>
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
