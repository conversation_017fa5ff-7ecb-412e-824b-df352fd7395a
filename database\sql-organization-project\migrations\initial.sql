-- Initial migration setup for the SQL organization project

-- Create tables
CREATE TABLE IF NOT EXISTS departments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    color_code VARCHAR(7),
    parent_id INT,
    FOREIGN KEY (parent_id) REFERENCES departments(id)
);

CREATE TABLE IF NOT EXISTS roles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(50) NOT NULL,
    description TEXT
);

CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    first_name <PERSON><PERSON><PERSON><PERSON>(50),
    last_name <PERSON><PERSON><PERSON><PERSON>(50),
    department_id INT,
    role_id INT,
    manager_id INT,
    is_first_login BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL,
    <PERSON><PERSON><PERSON><PERSON><PERSON> (department_id) REFERENCES departments(id),
    FOR<PERSON><PERSON><PERSON> (role_id) REFERENCES roles(id),
    FOREIGN KEY (manager_id) REFERENCES users(id)
);

CREATE TABLE IF NOT EXISTS functional_links (
    id INT AUTO_INCREMENT PRIMARY KEY,
    from_department_id INT,
    to_department_id INT,
    link_type VARCHAR(50),
    FOREIGN KEY (from_department_id) REFERENCES departments(id),
    FOREIGN KEY (to_department_id) REFERENCES departments(id)
);

-- Insert initial data
INSERT INTO roles (name, description) VALUES
('super_admin', 'Administrateur système avec tous les droits'),
('admin', 'Administrateur départemental'),
('manager', 'Manager d''équipe'),
('user', 'Utilisateur standard');

-- Insert initial departments
INSERT INTO departments (name, color_code) VALUES
('Direction Générale', '#808080'),
('Direction Commerciale', '#FF0000'),
('Direction Opérations', '#FFFF00'),
('Direction Support', '#0000FF');

-- Insert initial users (example)
INSERT INTO users (username, password, email, first_name, last_name, department_id, role_id) VALUES
('admin_user', 'hashed_password', '<EMAIL>', 'Admin', 'User', 1, 1),
('regular_user', 'hashed_password', '<EMAIL>', 'Regular', 'User', 2, 4);