<?php
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'schluter_db';

try {
    // Connexion à MySQL sans sélectionner la base de données
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Créer la base de données si elle n'existe pas
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    echo "Base de données créée avec succès<br>";

    // Sélectionner la base de données
    $pdo->exec("USE $database");

    // Lire et exécuter le fichier SQL
    $sql = file_get_contents('database/schluter_db.sql');
    $pdo->exec($sql);
    echo "Structure de la base de données importée avec succès";

} catch(PDOException $e) {
    die("Erreur : " . $e->getMessage());
}
?>
