<?php
// Script pour exécuter la migration de base de données
// Ajoute le support des fichiers PDF aux accusés de réception

error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function runMigration() {
    $conn = getDbConnection();
    
    try {
        // Vérifier si la colonne existe déjà
        $checkSql = "SHOW COLUMNS FROM accuses_reception LIKE 'pdf_file_path'";
        $result = $conn->query($checkSql);
        
        if ($result->rowCount() > 0) {
            return [
                'success' => true,
                'message' => 'La colonne pdf_file_path existe déjà. Migration non nécessaire.'
            ];
        }
        
        // Ajouter la colonne pdf_file_path
        $alterSql = "ALTER TABLE accuses_reception 
                     ADD COLUMN pdf_file_path VARCHAR(255) NULL 
                     COMMENT 'Chemin vers le fichier PDF importé (optionnel)' 
                     AFTER materiel_recu";
        
        $conn->exec($alterSql);
        
        // Ajouter un index pour améliorer les performances
        $indexSql = "CREATE INDEX idx_pdf_file_path ON accuses_reception(pdf_file_path)";
        $conn->exec($indexSql);
        
        return [
            'success' => true,
            'message' => 'Migration exécutée avec succès. Support PDF ajouté à la table accuses_reception.'
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors de la migration : ' . $e->getMessage()
        ];
    }
}

function createUploadDirectory() {
    $uploadDir = 'uploads/pdf/';
    
    if (!is_dir($uploadDir)) {
        if (mkdir($uploadDir, 0755, true)) {
            return [
                'success' => true,
                'message' => "Dossier d'upload créé : $uploadDir"
            ];
        } else {
            return [
                'success' => false,
                'message' => "Impossible de créer le dossier d'upload : $uploadDir"
            ];
        }
    } else {
        return [
            'success' => true,
            'message' => "Dossier d'upload existe déjà : $uploadDir"
        ];
    }
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migration Base de Données - Support PDF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .error { background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .info { background-color: #e8f4fd; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .btn { padding: 10px 20px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px; display: inline-block; margin: 10px 5px; }
        .btn:hover { background-color: #0056b3; }
        pre { background-color: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
<div class="container">
    <h1>🔧 Migration Base de Données - Support PDF</h1>
    
    <div class="info">
        <h3>📋 Cette migration va :</h3>
        <ul>
            <li>Ajouter une colonne <code>pdf_file_path</code> à la table <code>accuses_reception</code></li>
            <li>Créer un index pour améliorer les performances</li>
            <li>Créer le dossier d'upload sécurisé</li>
            <li>Vérifier que tout est en place pour le support PDF</li>
        </ul>
    </div>
    
    <?php if (isset($_POST['run_migration'])): ?>
        <h2>🚀 Exécution de la Migration</h2>
        
        <?php
        // Exécuter la migration de base de données
        $migrationResult = runMigration();
        ?>
        
        <div class="<?= $migrationResult['success'] ? 'success' : 'error' ?>">
            <strong>Migration Base de Données :</strong> <?= htmlspecialchars($migrationResult['message']) ?>
        </div>
        
        <?php
        // Créer le dossier d'upload
        $dirResult = createUploadDirectory();
        ?>
        
        <div class="<?= $dirResult['success'] ? 'success' : 'error' ?>">
            <strong>Dossier d'Upload :</strong> <?= htmlspecialchars($dirResult['message']) ?>
        </div>
        
        <?php if ($migrationResult['success'] && $dirResult['success']): ?>
            <div class="success">
                <h3>✅ Migration Terminée avec Succès !</h3>
                <p>Vous pouvez maintenant :</p>
                <ul>
                    <li>Utiliser les champs "Remis" et "Reçu" comme optionnels</li>
                    <li>Importer des fichiers PDF dans les accusés de réception</li>
                    <li>Consulter les PDF importés depuis l'interface</li>
                </ul>
                <a href="accuse_reception.php" class="btn">🔗 Aller aux Accusés de Réception</a>
            </div>
        <?php endif; ?>
        
    <?php else: ?>
        <h2>⚠️ Prêt à Exécuter la Migration</h2>
        
        <div class="info">
            <p><strong>Important :</strong> Cette opération va modifier la structure de la base de données. 
            Assurez-vous d'avoir une sauvegarde avant de continuer.</p>
        </div>
        
        <form method="POST">
            <button type="submit" name="run_migration" class="btn" 
                    onclick="return confirm('Êtes-vous sûr de vouloir exécuter cette migration ?')">
                🚀 Exécuter la Migration
            </button>
        </form>
        
        <h3>📄 Aperçu des Modifications SQL</h3>
        <pre><code>-- Ajouter le champ pour les fichiers PDF
ALTER TABLE accuses_reception 
ADD COLUMN pdf_file_path VARCHAR(255) NULL 
COMMENT 'Chemin vers le fichier PDF importé (optionnel)' 
AFTER materiel_recu;

-- Ajouter un index pour améliorer les performances
CREATE INDEX idx_pdf_file_path ON accuses_reception(pdf_file_path);</code></pre>
        
    <?php endif; ?>
    
    <div class="info">
        <h3>📚 Informations Techniques</h3>
        <ul>
            <li><strong>Taille max PDF :</strong> 10MB</li>
            <li><strong>Types autorisés :</strong> PDF uniquement</li>
            <li><strong>Stockage :</strong> uploads/pdf/ (sécurisé)</li>
            <li><strong>Encodage :</strong> UTF-8 pour les noms français</li>
        </ul>
    </div>
</div>
</body>
</html>
