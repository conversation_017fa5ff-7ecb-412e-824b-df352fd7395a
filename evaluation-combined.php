<?php
require_once __DIR__ . '/config/database.php';
session_start();

// Création des tables si elles n'existent pas
try {
    $db->exec("CREATE TABLE IF NOT EXISTS prestataires (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    $db->exec("CREATE TABLE IF NOT EXISTS services (
        id INT AUTO_INCREMENT PRIMARY KEY,
        nom VARCHAR(255) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    $db->exec("CREATE TABLE IF NOT EXISTS evaluations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prestataire VARCHAR(255) NOT NULL,
        service VARCHAR(255) NOT NULL,
        qualite INT NOT NULL,
        delai INT NOT NULL,
        communication INT NOT NULL,
        commentaires TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (prestataire) REFERENCES prestataires(nom) ON DELETE CASCADE,
        FOREIGN KEY (service) REFERENCES services(nom) ON DELETE CASCADE
    )");
} catch (PDOException $e) {
    die("Erreur lors de la création des tables: " . $e->getMessage());
}

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

// Function to handle database operations
function handleDb($action, $data = null) {
    global $db;
    
    switch ($action) {
        case "read":
            $stmt = $db->query("SELECT * FROM evaluations ORDER BY created_at DESC");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        case "write":
            $stmt = $db->prepare("INSERT INTO evaluations (prestataire, service, qualite, delai, communication, commentaires) VALUES (?, ?, ?, ?, ?, ?)");
            return $stmt->execute($data);        case "getProviders":
            try {
                // Récupérer les prestataires depuis la table prestataires
                return $db->query("SELECT nom as prestataire FROM prestataires ORDER BY nom")->fetchAll(PDO::FETCH_COLUMN);
            } catch (PDOException $e) {
                error_log("Erreur lors de la récupération des prestataires: " . $e->getMessage());
                return [];
            }        case "getServices":
            try {
                // Récupérer les services depuis la table services
                return $db->query("SELECT nom as service FROM services ORDER BY nom")->fetchAll(PDO::FETCH_COLUMN);
            } catch (PDOException $e) {
                error_log("Erreur lors de la récupération des services: " . $e->getMessage());
                return [];
            }            case "addProvider":
            try {
                $stmt = $db->prepare("INSERT INTO prestataires (nom) VALUES (?) ON DUPLICATE KEY UPDATE nom = nom");
                return $stmt->execute([$data]);
            } catch (PDOException $e) {
                error_log("Erreur lors de l'ajout du prestataire: " . $e->getMessage());
                throw $e;
            }
            
        case "deleteProvider":
            try {
                $stmt = $db->prepare("DELETE FROM prestataires WHERE nom = ?");
                return $stmt->execute([$data]);
            } catch (PDOException $e) {
                error_log("Erreur lors de la suppression du prestataire: " . $e->getMessage());
                throw $e;
            }            case "addService":
            try {
                $stmt = $db->prepare("INSERT INTO services (nom) VALUES (?) ON DUPLICATE KEY UPDATE nom = nom");
                return $stmt->execute([$data]);
            } catch (PDOException $e) {
                error_log("Erreur lors de l'ajout du service: " . $e->getMessage());
                throw $e;
            }
            
        case "deleteService":
            try {
                $stmt = $db->prepare("DELETE FROM services WHERE nom = ?");
                return $stmt->execute([$data]);
            } catch (PDOException $e) {
                error_log("Erreur lors de la suppression du service: " . $e->getMessage());
                throw $e;
            }
            
        case "getAverages":
            $query = "SELECT 
                service,
                prestataire,
                AVG(qualite) as moyenne_qualite,
                AVG(delai) as moyenne_delai,
                AVG(communication) as moyenne_communication,
                COUNT(*) as nombre_evaluations
            FROM evaluations 
            GROUP BY service, prestataire";
            
            $stmt = $db->query($query);
            $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $averages = [
                'Qualité' => [],
                'Rapidité' => [],
                'Communication' => [],
                'Total' => [],
                'Count' => []
            ];
            
            foreach ($results as $row) {
                $averages['Qualité'][$row['prestataire']] = floatval($row['moyenne_qualite']);
                $averages['Rapidité'][$row['prestataire']] = floatval($row['moyenne_delai']);
                $averages['Communication'][$row['prestataire']] = floatval($row['moyenne_communication']);
                $averages['Total'][$row['prestataire']] = 
                    (floatval($row['moyenne_qualite']) + 
                     floatval($row['moyenne_delai']) + 
                     floatval($row['moyenne_communication'])) / 3;
                $averages['Count'][$row['prestataire']] = intval($row['nombre_evaluations']);
            }
            
            return $averages;
    }
    return null;
}

// Handle POST requests
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $response = ['status' => 'success'];

    // Handle admin actions
    if (isset($_POST['action'])) {
        try {
            switch ($_POST['action']) {
                case 'addProvider':
                case 'deleteProvider':
                case 'addService':
                case 'deleteService':
                    if (!isset($_POST['name']) || empty($_POST['name'])) {
                        throw new Exception('Nom manquant');
                    }
                    handleDb($_POST['action'], $_POST['name']);
                    break;
                default:
                    throw new Exception('Action non valide');
            }
        } catch (Exception $e) {
            $response = ['status' => 'error', 'message' => $e->getMessage()];
            http_response_code(400);
        }
        echo json_encode($response);
        exit();
    }    // Handle evaluation submissions
    if (isset($_POST['provider'])) {
        if (isset($_POST['provider'], $_POST['serviceType'], $_POST['serviceQuality'],
            $_POST['deliveryTime'], $_POST['communication'])) {
            $data = [
                $_POST['provider'],
                $_POST['serviceType'],
                intval($_POST['serviceQuality']),
                intval($_POST['deliveryTime']),
                intval($_POST['communication']),
                $_POST['comments'] ?? ''
            ];
            try {
                handleDb("write", $data);
            } catch (PDOException $e) {
                $response = ['status' => 'error', 'message' => 'Database error: ' . $e->getMessage()];
                http_response_code(500);
            }
        } else {
            $response = ['status' => 'error', 'message' => 'Invalid form submission.'];
            http_response_code(400);
        }

        echo json_encode($response);
        exit();
    }
}

// Load data from database
$providers = handleDb("getProviders");
$services = handleDb("getServices");
$averages = handleDb("getAverages");
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Portail pour évaluer les prestataires et garantir la qualité des services.">
    <title>Évaluation des Prestataires - Schluter Systems</title>
    <link rel="stylesheet" href="css/evaluation-combined.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <?php include 'top_bar.php'; ?>

    <div class="evaluation-container">
        <div class="evaluation-header">
            <h1>Évaluation des Prestataires</h1>
            <p>Évaluez les prestataires pour garantir la qualité des services et améliorer les relations professionnelles.</p>
        </div>

        <div class="evaluation-grid">
            <!-- Left side: Provider list and statistics -->            <div class="providers-section">
                <div class="admin-controls">
                    <div class="search-container">
                        <input type="text" id="searchInput" class="form-control" placeholder="Rechercher un prestataire ou service...">
                    </div>
                    <button type="button" class="admin-btn" onclick="showModal('addProviderModal')">
                        <i class="fas fa-plus"></i> Ajouter un prestataire
                    </button>
                    <button type="button" class="admin-btn" onclick="showModal('addServiceModal')">
                        <i class="fas fa-plus"></i> Ajouter un service
                    </button>
                </div>

                <div class="providers-list">
                    <h2>Prestataires Disponibles</h2>
                    <?php foreach ($providers as $provider): ?>
                        <div class="provider-card">                            <div class="provider-header">
                                <h3><?php echo htmlspecialchars($provider); ?></h3>
                                <button type="button" class="delete-btn" onclick="deleteProvider('<?php echo htmlspecialchars($provider); ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                            <?php if (isset($averages['Total'][$provider])): ?>
                                <p>Note moyenne: <?php echo number_format($averages['Total'][$provider], 1); ?>/5</p>
                                <p>Nombre d'évaluations: <?php echo $averages['Count'][$provider]; ?></p>
                            <?php else: ?>
                                <p>Aucune évaluation</p>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>                </div>

                <div class="services-list">
                    <h2>Services Disponibles</h2>
                    <?php foreach ($services as $service): ?>
                        <div class="provider-card">
                            <div class="provider-header">
                                <h3><?php echo htmlspecialchars($service); ?></h3>
                                <button type="button" class="delete-btn" onclick="deleteService('<?php echo htmlspecialchars($service); ?>')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <div class="statistics-section">
                    <h2>Statistiques Globales</h2>
                    <?php if (empty($averages['Total'])): ?>
                        <div class="no-data-message">
                            <p><i class="fas fa-chart-bar"></i></p>
                            <p>Aucune donnée disponible. Les statistiques apparaîtront ici après l'ajout d'évaluations.</p>
                        </div>
                    <?php else: ?>
                        <div class="chart-container">
                            <canvas id="statsChart"></canvas>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Right side: Evaluation form -->
            <div class="evaluation-form">
                <h2>Nouvelle Évaluation</h2>
                <form id="evaluationForm">
                    <div class="form-group">
                        <label for="provider">Prestataire</label>
                        <select id="provider" name="provider" class="form-control" required>
                            <option value="">Sélectionnez un prestataire</option>
                            <?php foreach ($providers as $provider): ?>
                                <option value="<?php echo htmlspecialchars($provider); ?>">
                                    <?php echo htmlspecialchars($provider); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="serviceType">Type de Service</label>
                        <select id="serviceType" name="serviceType" class="form-control" required>
                            <option value="">Sélectionnez un service</option>
                            <?php foreach ($services as $service): ?>
                                <option value="<?php echo htmlspecialchars($service); ?>">
                                    <?php echo htmlspecialchars($service); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>Qualité du Service</label>
                        <div class="rating-group">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="serviceQuality" id="quality<?php echo $i; ?>" 
                                       value="<?php echo $i; ?>" required>
                                <label for="quality<?php echo $i; ?>"><?php echo $i; ?></label>
                            </div>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Respect des Délais</label>
                        <div class="rating-group">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="deliveryTime" id="delivery<?php echo $i; ?>" 
                                       value="<?php echo $i; ?>" required>
                                <label for="delivery<?php echo $i; ?>"><?php echo $i; ?></label>
                            </div>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Communication</label>
                        <div class="rating-group">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                            <div class="rating-option">
                                <input type="radio" name="communication" id="comm<?php echo $i; ?>" 
                                       value="<?php echo $i; ?>" required>
                                <label for="comm<?php echo $i; ?>"><?php echo $i; ?></label>
                            </div>
                            <?php endfor; ?>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="comments">Commentaires (optionnel)</label>
                        <textarea id="comments" name="comments" class="form-control" rows="4"></textarea>
                    </div>

                    <button type="submit" class="submit-btn">Soumettre l'évaluation</button>
                </form>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div class="modal-overlay" id="modalOverlay"></div>

    <!-- Add Provider Modal -->
    <div class="modal" id="addProviderModal">
        <div class="modal-header">
            <h3>Ajouter un prestataire</h3>
            <button class="modal-close" onclick="hideModals()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="providerName">Nom du prestataire</label>
                <input type="text" id="providerName" class="form-control" required>
            </div>
        </div>
        <div class="modal-footer">
            <button class="admin-btn" onclick="addProvider()">Ajouter</button>
            <button class="admin-btn cancel-btn" onclick="hideModals()">Annuler</button>
        </div>
    </div>

    <!-- Add Service Modal -->
    <div class="modal" id="addServiceModal">
        <div class="modal-header">
            <h3>Ajouter un service</h3>
            <button class="modal-close" onclick="hideModals()">&times;</button>
        </div>
        <div class="modal-body">
            <div class="form-group">
                <label for="serviceName">Nom du service</label>
                <input type="text" id="serviceName" class="form-control" required>
            </div>
        </div>
        <div class="modal-footer">
            <button class="admin-btn" onclick="addService()">Ajouter</button>
            <button class="admin-btn cancel-btn" onclick="hideModals()">Annuler</button>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteConfirmModal">
        <div class="modal-header">
            <h3>Confirmation de suppression</h3>
            <button class="modal-close" onclick="hideModals()">&times;</button>
        </div>
        <div class="modal-body">
            <p id="deleteConfirmMessage">Êtes-vous sûr de vouloir supprimer cet élément ?</p>
        </div>
        <div class="modal-footer">
            <button class="admin-btn delete-btn" onclick="confirmDelete()">Supprimer</button>
            <button class="admin-btn cancel-btn" onclick="hideModals()">Annuler</button>
        </div>
    </div>    <script>
    // Gestionnaire d'état global
    const AppState = {
        deleteCallback: null,
        deleteItemName: '',
        charts: new Map(),
        debounceTimers: new Map(),
        
        // Debounce helper
        debounce(fn, delay = 300) {
            return (...args) => {
                clearTimeout(this.debounceTimers.get(fn));
                this.debounceTimers.set(fn, setTimeout(() => fn.apply(this, args), delay));
            };
        }
    };

    // Optimized modal handling with keyboard support
    const Modal = {
        activeModal: null,
        
        show(modalId) {
            this.activeModal = document.getElementById(modalId);
            document.getElementById('modalOverlay').classList.add('active');
            this.activeModal.classList.add('active');
            
            // Focus first input in modal
            const firstInput = this.activeModal.querySelector('input, button');
            if (firstInput) firstInput.focus();
            
            // Add keyboard event listeners
            document.addEventListener('keydown', this.handleKeyPress);
        },
        
        hide() {
            if (this.activeModal) {
                document.getElementById('modalOverlay').classList.remove('active');
                this.activeModal.classList.remove('active');
                this.activeModal = null;
                document.removeEventListener('keydown', this.handleKeyPress);
            }
        },
        
        handleKeyPress(e) {
            if (e.key === 'Escape') {
                Modal.hide();
            } else if (e.key === 'Tab') {
                // Trap focus within modal
                const focusableElements = Modal.activeModal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );
                const firstFocusable = focusableElements[0];
                const lastFocusable = focusableElements[focusableElements.length - 1];
                
                if (e.shiftKey) {
                    if (document.activeElement === firstFocusable) {
                        e.preventDefault();
                        lastFocusable.focus();
                    }
                } else {
                    if (document.activeElement === lastFocusable) {
                        e.preventDefault();
                        firstFocusable.focus();
                    }
                }
            }
        }
    };

    // Replace existing modal functions with new Modal system
    function showModal(modalId) {
        Modal.show(modalId);
    }

    function hideModals() {
        Modal.hide();
    }

    // Initialize modal event listeners
    document.getElementById('modalOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            Modal.hide();
        }
    });

    function showDeleteConfirm(name, type, callback) {
        deleteCallback = callback;
        deleteItemName = name;
        document.getElementById('deleteConfirmMessage').textContent = 
            `Êtes-vous sûr de vouloir supprimer ${type} "${name}" ?`;
        showModal('deleteConfirmModal');
    }

    function confirmDelete() {
        if (deleteCallback) {
            deleteCallback(deleteItemName);
            deleteCallback = null;
        }
        hideModals();
    }    // Gestionnaire de notifications
    const Notify = {
        show(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.classList.add('show');
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => notification.remove(), 300);
                }, 3000);
            }, 100);
        }
    };

    async function handleAdminRequest(action, name) {
        try {
            // Valider les données avant l'envoi
            if (action.includes('Provider')) {
                FormValidator.validateProviderForm(name);
            } else if (action.includes('Service')) {
                FormValidator.validateServiceForm(name);
            }

            const button = document.activeElement;
            if (button) button.disabled = true;
            
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            document.body.appendChild(loadingIndicator);

            const formData = new FormData();
            formData.append('action', action);
            formData.append('name', name);
            
            const response = await fetch(window.location.href, {
                method: 'POST',
                body: formData
            });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const result = await response.json();
            if (result.status === 'success') {
                Notify.show('Opération réussie!', 'success');
                // Mise à jour optimisée sans rechargement complet
                setTimeout(() => window.location.reload(), 1000);
            } else {
                throw new Error(result.message || 'Une erreur est survenue');
            }
        } catch (error) {
            ErrorHandler.handle(error, 'Admin Request');
        } finally {
            const button = document.activeElement;
            if (button) button.disabled = false;
            const indicator = document.querySelector('.loading-indicator');
            if (indicator) indicator.remove();
        }
    }

    function addProvider() {
        const name = document.getElementById('providerName').value.trim();
        if (name) {
            handleAdminRequest('addProvider', name);
        }
        hideModals();
    }

    function deleteProvider(name) {
        showDeleteConfirm(name, 'le prestataire', () => handleAdminRequest('deleteProvider', name));
    }

    function addService() {
        const name = document.getElementById('serviceName').value.trim();
        if (name) {
            handleAdminRequest('addService', name);
        }
        hideModals();
    }

    function deleteService(name) {
        showDeleteConfirm(name, 'le service', () => handleAdminRequest('deleteService', name));
    }

    // Close modals when clicking overlay
    document.getElementById('modalOverlay').addEventListener('click', function(e) {
        if (e.target === this) {
            Modal.hide();
        }
    });

    // Initialize statistics chart with optimizations
    function initializeChart() {
        const ctx = document.getElementById('statsChart');
        if (!ctx) return; // Si l'élément n'existe pas (pas de données), on sort

        const averages = <?php echo json_encode($averages); ?>;
        const providers = Object.keys(averages['Total']);
        
        if (providers.length === 0) return; // Si pas de données, on ne crée pas le graphique
        
        const metrics = ['Qualité', 'Rapidité', 'Communication'];
        
        // Create datasets with optimized configuration
        const datasets = metrics.map((metric, index) => ({
            label: metric,
            data: providers.map(provider => averages[metric][provider] || 0),
            backgroundColor: [
                'rgba(255, 99, 132, 0.5)',
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 206, 86, 0.5)'
            ][index],
            borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)'
            ][index],
            borderWidth: 1,
            barPercentage: 0.8,
            categoryPercentage: 0.9
        }));

        // Store chart instance in AppState for later reference
        AppState.charts.set('statsChart', new Chart(ctx.getContext('2d'), {
            type: 'bar',
            data: {
                labels: providers,
                datasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                animation: {
                    duration: 400
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 5,
                        ticks: {
                            stepSize: 1
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            boxWidth: 12,
                            usePointStyle: true
                        }
                    }
                }
            }
        }));
    }

    // Update chart data with debouncing
    function updateChartData(newData) {
        const chart = AppState.charts.get('statsChart');
        if (!chart) return;

        const debouncedUpdate = AppState.debounce(() => {
            chart.data.datasets.forEach((dataset, index) => {
                dataset.data = newData[dataset.label].map(val => val || 0);
            });
            chart.update('none'); // Use 'none' for smooth updates
        }, 300);

        debouncedUpdate();
    }

    // Call initialization when DOM is loaded
    document.addEventListener('DOMContentLoaded', initializeChart);

    // Fonction de recherche filtrée
    function initializeSearch() {
        const searchInput = document.getElementById('searchInput');
        const providerCards = document.querySelectorAll('.provider-card');
        const serviceCards = document.querySelectorAll('.service-card');

        const filterItems = AppState.debounce((searchTerm) => {
            const term = searchTerm.toLowerCase();
            
            [...providerCards, ...serviceCards].forEach(card => {
                const title = card.querySelector('h3').textContent.toLowerCase();
                if (title.includes(term)) {
                    card.style.display = '';
                    card.classList.add('highlight-match');
                } else {
                    card.style.display = 'none';
                    card.classList.remove('highlight-match');
                }
            });
        }, 200);

        searchInput.addEventListener('input', (e) => filterItems(e.target.value));
    }

    // Initialiser la recherche au chargement
    document.addEventListener('DOMContentLoaded', initializeSearch);

    document.addEventListener('DOMContentLoaded', function() {
        // Handle form submission
        const form = document.getElementById('evaluationForm');
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const submitButton = form.querySelector('button[type="submit"]');
            submitButton.disabled = true;
            
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            document.body.appendChild(loadingIndicator);
            
            const formData = new FormData(form);
            try {
                FormValidator.validateEvaluationForm(formData);
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.status === 'success') {
                    Notify.show('Évaluation soumise avec succès!', 'success');
                    setTimeout(() => window.location.reload(), 1000);
                } else {
                    Notify.show(result.message || 'Une erreur est survenue', 'error');
                }
            } catch (error) {
                ErrorHandler.handle(error, 'Form Submission');
            } finally {
                submitButton.disabled = false;
                loadingIndicator.remove();
            }
        });
    });

    // Gestionnaire d'erreurs amélioré
    const ErrorHandler = {
        messages: {
            network: 'Problème de connexion au serveur',
            validation: 'Veuillez vérifier les champs du formulaire',
            server: 'Erreur serveur',
            unknown: 'Une erreur inattendue est survenue'
        },

        handle(error, context = '') {
            console.error(`${context} Error:`, error);
            
            let message = this.messages.unknown;
            if (error instanceof TypeError) {
                message = this.messages.network;
            } else if (error.validation) {
                message = error.message || this.messages.validation;
            }

            Notify.show(message, 'error');
        }
    };

    // Validateur de formulaire amélioré
    const FormValidator = {
        validateProviderForm(name) {
            if (!name) {
                throw { validation: true, message: 'Le nom du prestataire est requis' };
            }
            if (name.length < 2) {
                throw { validation: true, message: 'Le nom doit contenir au moins 2 caractères' };
            }
            return true;
        },

        validateServiceForm(name) {
            if (!name) {
                throw { validation: true, message: 'Le nom du service est requis' };
            }
            if (name.length < 2) {
                throw { validation: true, message: 'Le nom doit contenir au moins 2 caractères' };
            }
            return true;
        },

        validateEvaluationForm(formData) {
            const required = ['provider', 'serviceType', 'serviceQuality', 'deliveryTime', 'communication'];
            const missing = required.filter(field => !formData.get(field));
            
            if (missing.length > 0) {
                throw { 
                    validation: true, 
                    message: 'Veuillez remplir tous les champs obligatoires' 
                };
            }
            return true;
        }
    };
    </script>
</body>
</html>
