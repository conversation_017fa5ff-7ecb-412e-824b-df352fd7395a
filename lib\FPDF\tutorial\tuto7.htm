<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Ajout de polices et encodages</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Ajout de polices et encodages</h1>
Ce tutoriel explique comment ajouter des polices TrueType, OpenType et Type1 afin de ne plus se
limiter aux polices standards. L'autre intérêt est que l'on peut également choisir l'encodage des
caractères afin d'utiliser d'autres langues (les polices standards ne supportant que l'encodage cp1252).
<br>
<br>
Pour l'OpenType, seul le format basé sur le TrueType est supporté (pas celui basé sur le Type1).<br>
Pour les Type1, vous devez posséder le fichier AFM correspondant (il est généralement fourni avec la police).
<br>
<br>
L'ajout d'une police se fait en deux étapes :
<ul>
<li>Génération du fichier de définition de police</li>
<li>Déclaration de la police dans le script</li>
</ul>

<h2>Génération du fichier de définition de police</h2>
La première étape consiste à générer un fichier PHP contenant toutes les informations dont FPDF a
besoin ; on en profite également pour compresser le fichier de police. Pour cela, un script est
fourni dans le répertoire makefont de l'archive : makefont.php. Il contient la fonction suivante :
<br>
<br>
<code>MakeFont(<b>string</b> fontfile [, <b>string</b> enc [, <b>boolean</b> embed [, <b>boolean</b> subset]]])</code>
<dl class="param" style="margin-bottom:2em">
<dt><code>fontfile</code></dt>
<dd>
<p>Chemin du fichier .ttf, .otf ou .pfb.</p>
</dd>
<dt><code>enc</code></dt>
<dd>
<p>Nom de l'encodage à utiliser. Valeur par défaut : <code>cp1252</code>.</p>
</dd>
<dt><code>embed</code></dt>
<dd>
<p>Indique si la police doit être incorporée ou non. Valeur par défaut : <code>true</code>.</p>
</dd>
<dt><code>subset</code></dt>
<dd>
<p>Indique si le subsetting doit être utilisé. Valeur par défaut : <code>true</code>.</p>
</dd>
</dl>
Le premier paramètre est le nom du fichier de police. L'extension doit être .ttf, .otf ou .pfb et détermine
le type de la police. Si votre police Type1 est au format ASCII (.pfa), vous pouvez la convertir en binaire
(.pfb) grâce à <a href="http://www.lcdf.org/~eddietwo/type/#t1utils" target="_blank">t1utils</a>.
<br>
<br>
Pour les polices Type1, le fichier .afm correspondant doit se trouver dans le même répertoire.
<br>
<br>
L'encodage définit l'association entre un code (compris entre 0 et 255) et un caractère. Les 128
premières sont fixes et correspondent à l'ASCII ; les suivantes sont variables. Les encodages sont
stockés dans des fichiers .map. Ceux disponibles sont les suivants :
<ul>
<li>cp1250 (Europe Centrale)</li>
<li>cp1251 (cyrillique)</li>
<li>cp1252 (Europe de l'Ouest)</li>
<li>cp1253 (grec)</li>
<li>cp1254 (turc)</li>
<li>cp1255 (hébreu)</li>
<li>cp1257 (pays baltes)</li>
<li>cp1258 (vietnamien)</li>
<li>cp874 (thaïlandais)</li>
<li>ISO-8859-1 (Europe de l'Ouest)</li>
<li>ISO-8859-2 (Europe Centrale)</li>
<li>ISO-8859-4 (pays Baltes)</li>
<li>ISO-8859-5 (cyrillique)</li>
<li>ISO-8859-7 (grec)</li>
<li>ISO-8859-9 (turc)</li>
<li>ISO-8859-11 (thaïlandais)</li>
<li>ISO-8859-15 (Europe de l'Ouest)</li>
<li>ISO-8859-16 (Europe Centrale)</li>
<li>KOI8-R (russe)</li>
<li>KOI8-U (ukrainien)</li>
</ul>
Il faut bien sûr que la police contienne les caractères correspondant à l'encodage choisi.
<br>
<br>
Le troisième paramètre indique si la police doit être incorporée ou non. Lorsqu'une police
n'est pas incorporée, elle est recherchée dans le système. L'avantage est que le fichier PDF est
plus léger ; par contre, si elle n'est pas trouvée, une police de substitution est utilisée. Il
vaut donc mieux s'assurer que le système qui lit le PDF a bien la police installée. Pour assurer
un rendu correct dans tous les cas, il est fortement recommandé d'incorporer.
<br>
<br>
Le dernier paramètre indique si le subsetting doit être utilisé, c'est-à-dire si seuls les
caractères de l'encodage spécifié doivent être conservés dans la police incorporée. Cela
permet de réduire de manière importante la taille du fichier PDF, surtout si la police
d'origine était volumineuse.
<br>
<br>
Après avoir appelé la fonction (créez pour cela un nouveau fichier et incluez makefont.php), un
fichier .php est créé, avec le même nom que celui du fichier de police. Vous pouvez le renommer si
vous le souhaitez. Dans le cas de l'incorporation, le fichier de police est compressé et donne un
second fichier avec comme extension .z (sauf si la fonction de compression n'est pas disponible,
elle nécessite Zlib). Vous pouvez également le renommer, mais dans ce cas vous devez modifier la
variable <code>$file</code> en conséquence dans le fichier .php.
<br>
<br>
Exemple :
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'makefont/makefont.php'</span><span class="kw">);

</span>MakeFont<span class="kw">(</span><span class="str">'C:\\Windows\\Fonts\\comic.ttf'</span><span class="kw">,</span><span class="str">'cp1252'</span><span class="kw">);
</span>?&gt;</code></pre>
</div>
Ce qui donne les fichiers comic.php et comic.z.
<br>
<br>
Vous devez ensuite copier les fichiers générés dans le répertoire des polices. Si la police n'a
pas pu être compressée, il faut la copier directement à la place de la version compressée.
<br>
<br>
Il est également possible d'appeler MakeFont() via la ligne de commande :
<br>
<br>
<kbd>php makefont\makefont.php C:\Windows\Fonts\comic.ttf cp1252</kbd>
<br>
<br>
Enfin, pour les polices TrueType et OpenType, il est possible de générer les fichiers
<a href="http://www.fpdf.org/makefont/">en ligne</a> au lieu de le faire manuellement.

<h2>Déclaration de la police dans le script</h2>
La seconde étape est la plus simple. Il suffit d'appeler la méthode <a href='../doc/addfont.htm'>AddFont()</a> :
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'comic.php'</span><span class="kw">);
</span></code></pre>
</div>
Et la police est maintenant disponible (dans le style normal et souligné), utilisable comme les
autres. Si on avait traité le Comic Sans MS Gras (comicbd.ttf), on aurait mis :
<div class="source">
<pre><code>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'Comic'</span><span class="kw">,</span><span class="str">'B'</span><span class="kw">,</span><span class="str">'comicbd.php'</span><span class="kw">);
</span></code></pre>
</div>

<h2>Exemple</h2>
Voyons maintenant un exemple complet. La police utilisée est <a href="https://fonts.google.com/specimen/Ceviche+One" target="_blank">Ceviche One</a>.
La première étape consiste à générer les fichiers :
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'makefont/makefont.php'</span><span class="kw">);

</span>MakeFont<span class="kw">(</span><span class="str">'CevicheOne-Regular.ttf'</span><span class="kw">,</span><span class="str">'cp1252'</span><span class="kw">);
</span>?&gt;</code></pre>
</div>
L'exécution du script donne le compte-rendu suivant :
<br>
<br>
Font file compressed: CevicheOne-Regular.z<br>
Font definition file generated: CevicheOne-Regular.php<br>
<br>
Nous aurions également pu utiliser la ligne de commande :
<br>
<br>
<kbd>php makefont\makefont.php CevicheOne-Regular.ttf cp1252</kbd>
<br>
<br>
ou bien le générateur en ligne.
<br>
<br>
Nous pouvons maintenant copier les deux fichiers générés dans le répertoire des polices (font) et écrire le script :
<div class="source">
<pre><code>&lt;?php
<span class="kw">require(</span><span class="str">'fpdf.php'</span><span class="kw">);

</span>$pdf <span class="kw">= new </span>FPDF<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>AddFont<span class="kw">(</span><span class="str">'CevicheOne'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span><span class="str">'CevicheOne-Regular.php'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>AddPage<span class="kw">();
</span>$pdf<span class="kw">-&gt;</span>SetFont<span class="kw">(</span><span class="str">'CevicheOne'</span><span class="kw">,</span><span class="str">''</span><span class="kw">,</span>45<span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Write<span class="kw">(</span>10<span class="kw">,</span><span class="str">'Changez de police avec FPDF !'</span><span class="kw">);
</span>$pdf<span class="kw">-&gt;</span>Output<span class="kw">();
</span>?&gt;</code></pre>
</div>
<p class='demo'><a href='tuto7.php' target='_blank' class='demo'>[Exécuter]</a></p>
</body>
</html>
