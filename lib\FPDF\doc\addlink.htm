<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AddLink</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>AddLink</h1>
<code><b>int</b> AddLink()</code>
<h2>Description</h2>
Crée un nouveau lien interne et renvoie son identifiant. Un lien interne est une zone
cliquable qui amène à un autre endroit dans le document.
<br>
L'identifiant pourra être transmis aux méthodes Cell(), Write(), Image() ou Link(). La
destination est définie à l'aide de SetLink().
<h2>Voir</h2>
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="image.htm">Image</a>,
<a href="link.htm">Link</a>,
<a href="setlink.htm">SetLink</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
