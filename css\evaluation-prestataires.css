.banner {
    background: linear-gradient(135deg, #005ea2, #f57c00);
    color: white;
    text-align: center;
    padding: 60px 20px;
    margin-bottom: 40px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    margin-top: 70px;
}

.banner h1 {
    font-size: 3em;
    margin-bottom: 15px;
    font-weight: bold;
}

.banner p {
    font-size: 1.3em;
    margin-top: 10px;
    line-height: 1.6;
}

.services {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 20px;
    padding: 20px;
}

.service-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 300px;
    text-align: center;
    padding: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.service-card i {
    font-size: 3em;
    color: #f57c00;
    margin-bottom: 15px;
}

.service-card h3 {
    font-size: 1.5em;
    color: #333;
    margin-bottom: 10px;
}

.service-card p {
    font-size: 1em;
    color: #555;
    margin-bottom: 20px;
}

.button-group {
    display: flex;
    gap: 10px;
    justify-content: center;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: #f57c00;
}

.btn-primary:hover {
    background-color: #e67e22;
}

.btn-secondary {
    background-color: #2196f3;
}

.btn-secondary:hover {
    background-color: #1976d2;
}

.popup {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    width: 90%;
    max-width: 600px;
}

.popup.active {
    display: block;
}

.popup h2 {
    margin-bottom: 15px;
    color: #333;
}

.popup select {
    width: 100%;
    padding: 8px;
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.popup canvas {
    width: 100%;
    height: 300px;
    margin-top: 15px;
}

.close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: #e74c3c;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    cursor: pointer;
}

.close-btn:hover {
    background-color: #c0392b;
}
