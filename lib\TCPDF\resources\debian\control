Source: ~#P<PERSON>G<PERSON>ME#~
Maintainer: <PERSON> <<EMAIL>>
Section: php
Priority: optional
Build-Depends: debhelper (>= 9)
Standards-Version: 3.9.7
Homepage: https://github.com/~#VENDOR#~/~#PROJECT#~
Vcs-Git: https://github.com/~#VENDOR#~/~#PROJECT#~.git

Package: ~#PKGNAME#~
Provides: php-~#PROJECT#~
Architecture: all
Depends: php (>= 8.1.0), php-date, php-tecnickcom-tc-lib-barcode (<< 3.0.0), php-tecnickcom-tc-lib-barcode (>= 2.4.3), php-tecnickcom-tc-lib-color (<< 3.0.0), php-tecnickcom-tc-lib-color (>= 2.2.8), php-tecnickcom-tc-lib-pdf-image (<< 3.0.0), php-tecnickcom-tc-lib-pdf-image (>= 2.1.7), php-tecnickcom-tc-lib-pdf-font (<< 3.0.0), php-tecnickcom-tc-lib-pdf-font (>= 2.6.6), php-tecnickcom-tc-lib-file (<< 3.0.0), php-tecnickcom-tc-lib-file (>= 2.1.3), php-tecnickcom-tc-lib-pdf-encrypt (<< 3.0.0), php-tecnickcom-tc-lib-pdf-encrypt (>= 2.1.11), php-tecnickcom-tc-lib-unicode-data (<< 3.0.0), php-tecnickcom-tc-lib-unicode-data (>= 2.0.19), php-tecnickcom-tc-lib-unicode (<< 3.0.0), php-tecnickcom-tc-lib-unicode (>= 2.0.19), php-tecnickcom-tc-lib-pdf-page (<< 5.0.0), php-tecnickcom-tc-lib-pdf-page (>= 4.2.6), php-tecnickcom-tc-lib-pdf-graph (<< 3.0.0), php-tecnickcom-tc-lib-pdf-graph (>= 2.2.7), ${misc:Depends}
Description: PHP Barcode library
 This library includes PHP classes to generate PDF documents.
