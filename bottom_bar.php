<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <title>Page avec Footer Collé en Bas - Version Pro</title>
    <style>
        /* Reset de base */
        html, body {
            height: 100%;
            margin: 0;
            background-color: #fff; /* fond blanc, tu peux changer */
            font-family: Arial, sans-serif;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        main {
            flex: 1; /* Prend tout l’espace restant */
            padding: 20px;
        }

        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            width: 100%;
        }

        footer button {
            background-color: #f57c00;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            margin: 0 5px;
        }

        footer button:hover {
            background-color: #e67e22;
        }

        #backToTop {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: #f57c00;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            transition: background-color 0.3s ease;
            display: none;
            font-weight: bold;
            font-size: 18px;
            line-height: 1;
        }

        #backToTop:hover {
            background-color: #333;
        }
    </style>
</head>
<body>

    <main>
        <!-- Contenu principal, vide ou rempli -->
    </main>

    <footer>
        <p>&copy; 2025 Schluter Systems. Tous droits réservés.</p>
        <!-- Si tu veux des boutons dans le footer, tu peux les ajouter ici -->
        <!-- <button>Exemple</button> -->
    </footer>

    <button id="backToTop" onclick="scrollToTop()">↑</button>

    <script>
        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>

</body>
</html>
