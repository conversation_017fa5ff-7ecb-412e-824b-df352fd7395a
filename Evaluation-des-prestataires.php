<?php
require_once('config/database.php');
session_start();

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php');
    exit();
}

function getServices() {
    global $db;
    $stmt = $db->query("SELECT DISTINCT service FROM evaluations ORDER BY service");
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getAverages() {
    global $db;
    $query = "SELECT 
        service,
        prestataire,
        AVG(qualite) as moyenne_qualite,
        AVG(delai) as moyenne_delai,
        AVG(communication) as moyenne_communication,
        COUNT(*) as nombre_evaluations
    FROM evaluations 
    GROUP BY service, prestataire";
    
    $stmt = $db->query($query);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $averages = [
        'Qualité' => [],
        'Rapidité' => [],
        'Communication' => [],
        'Total' => [],
        'Count' => []
    ];
    
    foreach ($results as $row) {
        $averages['Qualité'][$row['prestataire']] = floatval($row['moyenne_qualite']);
        $averages['Rapidité'][$row['prestataire']] = floatval($row['moyenne_delai']);
        $averages['Communication'][$row['prestataire']] = floatval($row['moyenne_communication']);
        $averages['Total'][$row['prestataire']] = 
            (floatval($row['moyenne_qualite']) + 
             floatval($row['moyenne_delai']) + 
             floatval($row['moyenne_communication'])) / 3;
        $averages['Count'][$row['prestataire']] = intval($row['nombre_evaluations']);
    }
    
    return $averages;
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Portail pour évaluer les prestataires et garantir la qualité des services.">
    <title>Évaluation des Prestataires - Schluter Systems</title>    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="css/evaluation-prestataires.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            
        <style>
           {
            background: linear-gradient(135deg, #005ea2, #f57c00);
            color: white;
            text-align: center;
            padding: 60px 20px;
            margin-bottom: 40px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 100%;
            margin-top: 70px; 
           }
        

        .banner h1 {
            font-size: 3em;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .banner p {
            font-size: 1.3em;
            margin-top: 10px;
            line-height: 1.6;
        }

        .services {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }

        .service-card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            width: 300px;
            text-align: center;
            padding: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
        }

        .service-card i {
            font-size: 3em;
            color: #f57c00;
            margin-bottom: 15px;
        }

        .service-card h3 {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 10px;
        }

        .service-card p {
            font-size: 1em;
            color: #555;
            margin-bottom: 20px;
        }

        .service-card a {
            display: inline-block;
            padding: 10px 20px;
            background-color: #f57c00;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s ease;
        }

        .service-card a:hover {
            background-color: #e67e22;
        }

        .popup {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            z-index: 1000;
            width: 400px;
        }

        .popup.active {
            display: block;
        }

        .popup h2 {
            margin-bottom: 15px;
            color: #333;
        }

        .popup button {
            margin-top: 10px;
            padding: 10px;
            background-color: #f57c00;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        .popup button:hover {
            background-color: #e67e22;
        }

        .popup .close-btn {
            background-color: #e74c3c;
            float: right;
        }

        .popup .close-btn:hover {
            background-color: #c0392b;
        }

        .popup canvas {
            width: 100%;
            height: 300px;
        }
    </style>
</head>
<body>
<?php include 'top_bar.php'; ?>

<div class="banner">
    <h1>Évaluation des Prestataires</h1>
    <p>Évaluez les prestataires pour garantir la qualité des services et améliorer les relations professionnelles.</p>
</div>

<div class="services">
    <!-- Carte pour Evaluation -->
    <div class="service-card">
        <i class="fas fa-edit"></i>
        <h3>Nouvelle Évaluation</h3>
        <p>Évaluez un prestataire dès maintenant.</p>
        <a href="evaluation.php">Commencer</a>
    </div>

    <!-- Carte pour Historique des Évaluations -->
    <div class="service-card">
        <i class="fas fa-history"></i>
        <h3>Historique des Évaluations</h3>
        <p>Consultez l'historique de vos évaluations.</p>
        <a href="historique_evaluations.php">Voir l'historique</a>
    </div>

    <!-- Carte pour Moyenne de Satisfaction -->
    <div class="service-card">
        <i class="fas fa-chart-bar"></i>
        <h3>Moyenne de Satisfaction</h3>
        <p>Voir les moyennes de satisfaction des prestataires.</p>
        <a href="moyenne_satisfaction.php">Voir les moyennes</a>
    </div>
</div>
<?php include 'bottom_bar.php'; ?>

<script>
    const averages = <?= json_encode($averages) ?>;

    function togglePopup() {
        const popup = document.getElementById('popup');
        popup.classList.toggle('active');
    }

    function updateChart() {
        const provider = document.getElementById('providerSelect').value;
        const ctx = document.getElementById('providerChart').getContext('2d');

        if (window.providerChartInstance) {
            window.providerChartInstance.destroy();
        }

        if (provider && averages['Qualité'][provider]) {
            window.providerChartInstance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Qualité', 'Rapidité', 'Communication', 'Total', 'Nombre d\'Évaluations'],
                    datasets: [{
                        label: `Moyennes pour ${provider}`,
                        data: [
                            averages['Qualité'][provider],
                            averages['Rapidité'][provider],
                            averages['Communication'][provider],
                            averages['Total'][provider],
                            averages['Count'][provider]
                        ],
                        backgroundColor: ['#f57c00', '#4caf50', '#2196f3', '#9c27b0', '#ff5722'],
                        borderColor: ['#e67e22', '#388e3c', '#1976d2', '#7b1fa2', '#d84315'],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return `${context.label}: ${context.raw.toFixed(2)}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Score'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: 'Critères'
                            }
                        }
                    }
                }
            });
        }
    }
</script>
</body>
</html>
