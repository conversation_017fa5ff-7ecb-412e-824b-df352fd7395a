<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Rect</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Rect</h1>
<code>Rect(<b>float</b> x, <b>float</b> y, <b>float</b> w, <b>float</b> h [, <b>string</b> style])</code>
<h2>Description</h2>
Dessine un rectangle à partir de son coin supérieur gauche. Il peut être tracé, rempli ou les
deux à la fois.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
Abscisse du coin supérieur gauche.
</dd>
<dt><code>y</code></dt>
<dd>
Ordonnée du coin supérieur gauche.
</dd>
<dt><code>w</code></dt>
<dd>
Largeur.
</dd>
<dt><code>h</code></dt>
<dd>
Hauteur.
</dd>
<dt><code>style</code></dt>
<dd>
Manière de tracer le rectangle. Les valeurs possibles sont :
<ul>
<li><code>D</code> ou chaîne vide : contour (draw). C'est la valeur par défaut.
<li><code>F</code> : remplissage (fill)
<li><code>DF</code> ou <code>FD</code> : contour et remplissage
</ul>
</dd>
</dl>
<h2>Voir</h2>
<a href="setlinewidth.htm">SetLineWidth</a>,
<a href="setdrawcolor.htm">SetDrawColor</a>,
<a href="setfillcolor.htm">SetFillColor</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
