<?php
// Test de la nouvelle fonction de suppression corrigée
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Nouvelle fonction de suppression corrigée
function deleteAccuseReception($deleteId) {
    try {
        $conn = getDbConnection();
        
        // Vérifier si l'enregistrement existe avant suppression
        $checkSql = "SELECT collaborateur, responsable FROM accuses_reception WHERE id = :id";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->execute([':id' => $deleteId]);
        $record = $checkStmt->fetch();
        
        if (!$record) {
            return [
                'success' => false, 
                'message' => "Erreur : Aucun accusé de réception trouvé avec l'ID $deleteId"
            ];
        }
        
        // Effectuer la suppression
        $sql = "DELETE FROM accuses_reception WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([':id' => $deleteId]);
        
        if ($result && $stmt->rowCount() > 0) {
            return [
                'success' => true, 
                'message' => "Accusé de réception de {$record['collaborateur']} supprimé avec succès"
            ];
        } else {
            return [
                'success' => false, 
                'message' => "Erreur : Impossible de supprimer l'accusé de réception"
            ];
        }
        
    } catch (PDOException $e) {
        return [
            'success' => false, 
            'message' => "Erreur de base de données : " . $e->getMessage()
        ];
    }
}

// Lister les enregistrements disponibles
function listRecords() {
    echo "<h3>Enregistrements disponibles pour test:</h3>";
    
    try {
        $conn = getDbConnection();
        $sql = "SELECT id, collaborateur, responsable, date_creation FROM accuses_reception ORDER BY date_creation DESC";
        $stmt = $conn->query($sql);
        $records = $stmt->fetchAll();
        
        if (empty($records)) {
            echo "Aucun enregistrement trouvé.<br>";
            return;
        }
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background-color: #f8f9fa;'><th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>Action</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . $record['date_creation'] . "</td>";
            echo "<td><a href='?test_delete=" . $record['id'] . "' onclick='return confirm(\"Êtes-vous sûr de vouloir supprimer l\\\"accusé de réception de " . htmlspecialchars($record['collaborateur']) . " ?\")' style='background-color: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>🗑️ Tester suppression</a></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (PDOException $e) {
        echo "❌ Erreur lors de la récupération des données: " . $e->getMessage() . "<br>";
    }
}

// Traitement des paramètres
if (isset($_GET['test_delete'])) {
    $deleteId = intval($_GET['test_delete']);
    if ($deleteId > 0) {
        echo "<h3>Test de suppression pour ID: $deleteId</h3>";
        $result = deleteAccuseReception($deleteId);
        
        if ($result['success']) {
            echo "<div style='background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
            echo "✅ " . $result['message'];
            echo "</div>";
        } else {
            echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
            echo "❌ " . $result['message'];
            echo "</div>";
        }
        
        echo "<br><a href='test_new_delete_function.php' style='background-color: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 4px;'>← Retour à la liste</a><br><br>";
    } else {
        echo "<div style='background-color: #f8d7da; color: #721c24; padding: 15px; border-radius: 4px; margin: 10px 0;'>";
        echo "❌ ID invalide";
        echo "</div>";
    }
}

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head><meta charset='UTF-8'><title>Test Fonction Suppression</title></head>";
echo "<body style='font-family: Arial, sans-serif; margin: 20px;'>";
echo "<h1>Test de la nouvelle fonction de suppression</h1>";

if (!isset($_GET['test_delete'])) {
    listRecords();
}

echo "<hr>";
echo "<h3>✅ Améliorations apportées:</h3>";
echo "<ul>";
echo "<li>✅ Gestion d'erreurs complète avec try/catch</li>";
echo "<li>✅ Vérification de l'existence de l'enregistrement avant suppression</li>";
echo "<li>✅ Messages de retour détaillés (succès/erreur)</li>";
echo "<li>✅ Validation de l'ID (doit être > 0)</li>";
echo "<li>✅ Retour du nombre de lignes affectées</li>";
echo "<li>✅ Messages personnalisés avec nom du collaborateur</li>";
echo "</ul>";

echo "<h3>🔧 Corrections dans accuse_reception.php:</h3>";
echo "<ul>";
echo "<li>✅ URL de redirection corrigée</li>";
echo "<li>✅ Confirmation JavaScript ajoutée</li>";
echo "<li>✅ Messages de feedback pour l'utilisateur</li>";
echo "<li>✅ Styles CSS pour les messages et boutons</li>";
echo "<li>✅ Redirection après suppression pour éviter la re-soumission</li>";
echo "</ul>";

echo "</body></html>";
?>
