-- Création de la table des prestataires
CREATE TABLE IF NOT EXISTS prestataires (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Création de la table des services
CREATE TABLE IF NOT EXISTS services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(255) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Modification de la table evaluations pour ajouter les contraintes de clé étrangère
ALTER TABLE evaluations
ADD FOREIGN KEY (prestataire) REFERENCES prestataires(nom) ON DELETE CASCADE ON UPDATE CASCADE,
ADD FOREIGN KEY (service) REFERENCES services(nom) ON DELETE CASCADE ON UPDATE CASCADE;
