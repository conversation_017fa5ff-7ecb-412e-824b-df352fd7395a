<?php
// Test de génération PDF avec les nouvelles fonctionnalités UTF-8
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');

// Classe FPDF étendue pour supporter l'UTF-8 (copie de celle dans accuse_reception.php)
class FPDF_UTF8 extends FPDF
{
    // Tableau de conversion des caractères UTF-8 vers ISO-8859-1
    private $utf8_to_iso = array(
        'à' => 'à', 'á' => 'á', 'â' => 'â', 'ã' => 'ã', 'ä' => 'ä', 'å' => 'å',
        'è' => 'è', 'é' => 'é', 'ê' => 'ê', 'ë' => 'ë',
        'ì' => 'ì', 'í' => 'í', 'î' => 'î', 'ï' => 'ï',
        'ò' => 'ò', 'ó' => 'ó', 'ô' => 'ô', 'õ' => 'õ', 'ö' => 'ö',
        'ù' => 'ù', 'ú' => 'ú', 'û' => 'û', 'ü' => 'ü',
        'ç' => 'ç', 'ñ' => 'ñ',
        'À' => 'À', 'Á' => 'Á', 'Â' => 'Â', 'Ã' => 'Ã', 'Ä' => 'Ä', 'Å' => 'Å',
        'È' => 'È', 'É' => 'É', 'Ê' => 'Ê', 'Ë' => 'Ë',
        'Ì' => 'Ì', 'Í' => 'Í', 'Î' => 'Î', 'Ï' => 'Ï',
        'Ò' => 'Ò', 'Ó' => 'Ó', 'Ô' => 'Ô', 'Õ' => 'Õ', 'Ö' => 'Ö',
        'Ù' => 'Ù', 'Ú' => 'Ú', 'Û' => 'Û', 'Ü' => 'Ü',
        'Ç' => 'Ç', 'Ñ' => 'Ñ',
        '€' => '€', '£' => '£', '¥' => '¥', '§' => '§', '©' => '©', '®' => '®',
        '°' => '°', '±' => '±', '²' => '²', '³' => '³', 'µ' => 'µ', '¶' => '¶',
        '¼' => '¼', '½' => '½', '¾' => '¾'
    );
    
    private function convertText($txt)
    {
        // Si le texte est vide, le retourner tel quel
        if (empty($txt)) {
            return $txt;
        }
        
        // Si le texte est en UTF-8, le convertir
        if (mb_check_encoding($txt, 'UTF-8')) {
            // Utiliser iconv si disponible (plus fiable pour FPDF)
            if (function_exists('iconv')) {
                $converted = iconv('UTF-8', 'ISO-8859-1//IGNORE', $txt);
                if ($converted !== false) {
                    return $converted;
                }
            }
            
            // Fallback: utiliser notre tableau de correspondance
            $converted = strtr($txt, $this->utf8_to_iso);
            
            // Si ça ne marche toujours pas, essayer mb_convert_encoding
            if ($converted === $txt) {
                $converted = mb_convert_encoding($txt, 'ISO-8859-1', 'UTF-8');
            }
            
            return $converted;
        }
        
        return $txt;
    }
    
    function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }
    
    function MultiCell($w, $h, $txt, $border=0, $align='J', $fill=false)
    {
        $txt = $this->convertText($txt);
        parent::MultiCell($w, $h, $txt, $border, $align, $fill);
    }
    
    function Write($h, $txt, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Write($h, $txt, $link);
    }
}

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

// Récupérer les données de test
$conn = getDbConnection();
$sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable, 
        date_creation as Date, materiel_remis as Remis, materiel_recu as Recu 
        FROM accuses_reception 
        ORDER BY date_creation DESC LIMIT 1";
$stmt = $conn->query($sql);
$ar = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$ar) {
    die("Aucune donnée trouvée. Exécutez d'abord test_insert_data.php");
}

// Fonction pour nettoyer et convertir le texte
function cleanText($text) {
    $text = strip_tags($text);
    if (!mb_check_encoding($text, 'UTF-8')) {
        $text = mb_convert_encoding($text, 'UTF-8', 'auto');
    }
    return $text;
}

// Générer le PDF
$pdf = new FPDF_UTF8();
$pdf->AddPage();
$pdf->SetMargins(20, 20, 20);

// Couleurs de l'entreprise
$pdf->SetTextColor(0, 70, 150);

// Titre principal
$pdf->SetFont('Arial', 'B', 35);
$pdf->SetY(10);
$pdf->Cell(0, 10, 'Accusé de réception', 0, 1, 'L');
$pdf->Ln(20);

// Contenu
$pdf->SetFont('Arial', 'I', 15);
$pdf->SetTextColor(50, 50, 50);
$pdf->Cell(0, 10, "Je soussigné " . cleanText($ar['Collaborateur']), 0, 1, 'L');
$pdf->Cell(0, 10, "Atteste avoir remis le(s) matériel(s) ou le(s) document(s) suivant(s) :", 0, 1, 'L');
$pdf->Ln(10);
$pdf->SetFont('Arial', '', 12);
$pdf->SetTextColor(0, 0, 0);
$pdf->MultiCell(0, 10, cleanText($ar['Remis']), 0, 'L');
$pdf->Ln(20);
$pdf->SetFont('Arial', 'I', 15);
$pdf->SetTextColor(50, 50, 50);
$pdf->Cell(0, 10, "Atteste avoir reçu le(s) matériel(s) ou le(s) document(s) suivant(s) :", 0, 1, 'L');
$pdf->Ln(10);
$pdf->SetFont('Arial', '', 12);
$pdf->SetTextColor(0, 0, 0);
$pdf->MultiCell(0, 10, cleanText($ar['Recu']), 0, 'L');
$pdf->Ln(30);
$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 10, "Du Service Informatique par " . cleanText($ar['Responsable']), 0, 1, 'L');
$pdf->Cell(0, 10, "En date du " . date('d/m/Y', strtotime($ar['Date'])), 0, 1, 'L');
$pdf->Ln(20);
$pdf->SetFont('Arial', 'B', 15);
$pdf->Cell(95, 10, "Bon pour accord du responsable", 1, 0, 'C');
$pdf->Cell(95, 10, "Bon pour accord du collaborateur", 1, 1, 'C');

$filename = 'test_accuse_reception_utf8.pdf';
$pdf->Output('F', $filename);

echo "PDF généré avec succès: $filename<br>";
echo "Données utilisées:<br>";
echo "- Collaborateur: " . $ar['Collaborateur'] . "<br>";
echo "- Responsable: " . $ar['Responsable'] . "<br>";
echo "- Matériel remis: " . $ar['Remis'] . "<br>";
echo "- Matériel reçu: " . $ar['Recu'] . "<br>";
echo "<br>Vérifiez que tous les caractères français s'affichent correctement dans le PDF.";
?>
