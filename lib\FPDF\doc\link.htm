<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Link</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Link</h1>
<code>Link(<b>float</b> x, <b>float</b> y, <b>float</b> w, <b>float</b> h, <b>mixed</b> link)</code>
<h2>Description</h2>
Place un lien sur une zone rectangulaire de la page. Les liens textes ou images se posent
généralement via Cell(), Write() ou Image(), mais cette méthode peut être utile par exemple
pour placer une zone cliquable à l'intérieur d'une image.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
Abscisse du coin supérieur gauche du rectangle.
</dd>
<dt><code>y</code></dt>
<dd>
Ordonnée du coin supérieur gauche du rectangle.
</dd>
<dt><code>w</code></dt>
<dd>
Largeur du rectangle.
</dd>
<dt><code>h</code></dt>
<dd>
Hauteur du rectangle.
</dd>
<dt><code>link</code></dt>
<dd>
URL ou identifiant retourné par AddLink().
</dd>
</dl>
<h2>Voir</h2>
<a href="addlink.htm">AddLink</a>,
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="image.htm">Image</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
