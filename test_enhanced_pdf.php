<?php
// Test des améliorations visuelles des PDFs
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');

// Inclure la classe FPDF_UTF8 améliorée
include_once('accuse_reception.php');

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test PDFs Améliorés</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fa; }";
echo ".container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }";
echo ".success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 4px solid #28a745; }";
echo ".info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 4px solid #17a2b8; }";
echo ".warning { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; border-left: 4px solid #ffc107; }";
echo "table { border-collapse: collapse; width: 100%; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
echo "th { background-color: #f8f9fa; font-weight: bold; }";
echo ".btn { padding: 10px 20px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px; font-weight: bold; transition: all 0.3s; }";
echo ".btn-primary { background-color: #007bff; color: white; }";
echo ".btn-success { background-color: #28a745; color: white; }";
echo ".btn-info { background-color: #17a2b8; color: white; }";
echo ".btn:hover { opacity: 0.8; transform: translateY(-1px); }";
echo ".feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }";
echo ".feature-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }";
echo ".feature-title { font-weight: bold; color: #007bff; margin-bottom: 10px; }";
echo "h1 { color: #2c3e50; text-align: center; margin-bottom: 30px; }";
echo "h2 { color: #34495e; border-bottom: 2px solid #3498db; padding-bottom: 10px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<div class='container'>";
echo "<h1>🎨 Test des PDFs Améliorés - Schlüter Systems</h1>";

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        echo "<div class='warning'>❌ Erreur de connexion : " . $e->getMessage() . "</div>";
        return null;
    }
}

// Afficher les améliorations apportées
function displayEnhancements() {
    echo "<h2>✨ Améliorations Visuelles Implémentées</h2>";
    
    echo "<div class='feature-grid'>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>🎨 Palette de Couleurs Professionnelle</div>";
    echo "<ul>";
    echo "<li>Bleu Schlüter (0, 70, 150) pour les titres</li>";
    echo "<li>Orange accent (230, 126, 34) pour les sections</li>";
    echo "<li>Gris élégant pour le texte et bordures</li>";
    echo "<li>Fond subtil pour les encadrés</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>📐 Mise en Page Améliorée</div>";
    echo "<ul>";
    echo "<li>Marges optimisées (25mm)</li>";
    echo "<li>Espacement cohérent entre sections</li>";
    echo "<li>Alignement professionnel des éléments</li>";
    echo "<li>Hiérarchie visuelle claire</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>🔤 Typographie Améliorée</div>";
    echo "<ul>";
    echo "<li>Titre principal en 28pt</li>";
    echo "<li>Sections en 13pt avec couleur accent</li>";
    echo "<li>Texte corps en 11pt lisible</li>";
    echo "<li>Styles gras/italique appropriés</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>🏢 Logo et Branding</div>";
    echo "<ul>";
    echo "<li>Positionnement optimisé du logo</li>";
    echo "<li>Fallback textuel élégant</li>";
    echo "<li>Identité visuelle cohérente</li>";
    echo "<li>En-tête professionnel</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>✍️ Zones de Signature</div>";
    echo "<ul>";
    echo "<li>Boîtes de signature élégantes</li>";
    echo "<li>Lignes pour signatures manuscrites</li>";
    echo "<li>Titres clairs pour chaque signataire</li>";
    echo "<li>Espacement optimal</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div class='feature-card'>";
    echo "<div class='feature-title'>📄 Pied de Page</div>";
    echo "<ul>";
    echo "<li>Numérotation des pages</li>";
    echo "<li>Informations de l'entreprise</li>";
    echo "<li>Date de génération</li>";
    echo "<li>Séparateurs visuels</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
}

// Afficher les enregistrements pour test
function displayTestRecords() {
    echo "<h2>📋 Enregistrements Disponibles pour Test</h2>";
    
    $conn = getDbConnection();
    if (!$conn) return;
    
    try {
        $sql = "SELECT id, collaborateur, responsable, date_creation, materiel_remis, materiel_recu FROM accuses_reception ORDER BY date_creation DESC LIMIT 5";
        $stmt = $conn->query($sql);
        $records = $stmt->fetchAll();
        
        if (empty($records)) {
            echo "<div class='info'>ℹ️ Aucun enregistrement trouvé. <a href='test_insert_data.php' class='btn btn-primary'>Créer des données de test</a></div>";
            return;
        }
        
        echo "<div class='info'>";
        echo "<strong>Instructions :</strong> Cliquez sur les boutons ci-dessous pour générer des PDFs avec le nouveau design amélioré.";
        echo "</div>";
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>Actions</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . $record['date_creation'] . "</td>";
            echo "<td>";
            echo "<a href='accuse_reception.php?download_single_pdf=" . $record['id'] . "' class='btn btn-primary'>📄 PDF Individuel</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        echo "<div style='margin-top: 20px; text-align: center;'>";
        echo "<a href='accuse_reception.php?download_pdf=1' class='btn btn-success'>📄 PDF Global (Tous les accusés)</a>";
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<div class='warning'>❌ Erreur lors de la récupération des données: " . $e->getMessage() . "</div>";
    }
}

// Comparaison avant/après
function displayComparison() {
    echo "<h2>🔄 Comparaison Avant/Après</h2>";
    
    echo "<table>";
    echo "<tr><th>Aspect</th><th>❌ Avant</th><th>✅ Après</th></tr>";
    
    $comparisons = [
        "Titre" => ["Arial 35pt noir basique", "Arial 28pt bleu Schlüter avec sous-titre"],
        "Logo" => ["Chemin fixe, erreur si absent", "Chemins multiples + fallback textuel"],
        "Couleurs" => ["Gris foncé monotone", "Palette professionnelle Schlüter"],
        "Espacement" => ["Ln() arbitraire", "Espacement cohérent et calculé"],
        "Sections" => ["Texte simple", "Encadrés colorés avec séparateurs"],
        "Signatures" => ["Tableaux basiques", "Boîtes élégantes avec lignes"],
        "Pied de page" => ["Absent", "Numérotation + infos entreprise"],
        "Mise en page" => ["Marges 20mm", "Marges 25mm optimisées"],
        "Typographie" => ["Tailles limitées", "Hiérarchie visuelle complète"]
    ];
    
    foreach ($comparisons as $aspect => $comparison) {
        echo "<tr>";
        echo "<td><strong>$aspect</strong></td>";
        echo "<td>" . $comparison[0] . "</td>";
        echo "<td>" . $comparison[1] . "</td>";
        echo "</tr>";
    }
    
    echo "</table>";
}

// Afficher le contenu
displayEnhancements();
displayComparison();
displayTestRecords();

echo "<hr style='margin: 40px 0;'>";
echo "<div class='success'>";
echo "<h3>🎯 Points de Validation :</h3>";
echo "<ul>";
echo "<li>✅ <strong>Design professionnel</strong> : Couleurs et mise en page Schlüter</li>";
echo "<li>✅ <strong>Lisibilité améliorée</strong> : Typographie et espacement optimisés</li>";
echo "<li>✅ <strong>Branding cohérent</strong> : Logo et identité visuelle</li>";
echo "<li>✅ <strong>Fonctionnalité préservée</strong> : UTF-8 et génération PDF intacts</li>";
echo "<li>✅ <strong>Signatures élégantes</strong> : Zones dédiées avec lignes</li>";
echo "<li>✅ <strong>Navigation claire</strong> : Pied de page informatif</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin-top: 30px;'>";
echo "<a href='accuse_reception.php' class='btn btn-info'>🔙 Retour à la page principale</a>";
echo " ";
echo "<a href='test_insert_data.php' class='btn btn-primary'>➕ Créer des données de test</a>";
echo "</div>";

echo "</div>";
echo "</body></html>";
?>
