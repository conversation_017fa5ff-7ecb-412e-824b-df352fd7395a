<?php
require_once 'config/database.php';

function migrateCsvToDb($csvFile, $tableName, $columns) {
    global $db;
    
    if (($handle = fopen($csvFile, "r")) !== FALSE) {
        // Skip header row
        fgetcsv($handle, 1000, ",");
        
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $placeholders = str_repeat('?,', count($columns) - 1) . '?';
            $sql = "INSERT INTO $tableName (" . implode(',', $columns) . ") VALUES ($placeholders)";
            
            try {
                $stmt = $db->prepare($sql);
                $stmt->execute($data);
                echo "Ligne migrée avec succès dans $tableName\n";
            } catch(PDOException $e) {
                echo "Erreur lors de l'insertion dans $tableName : " . $e->getMessage() . "\n";
            }
        }
        fclose($handle);
        echo "Migration terminée pour $tableName\n\n";
    } else {
        echo "Impossible d'ouvrir le fichier $csvFile\n";
    }
}

// Migration des évaluations
echo "Migration des évaluations...\n";
migrateCsvToDb(
    "evaluations.csv",
    "evaluations",
    ['prestataire', 'service', 'qualite', 'delai', 'communication', 'commentaires']
);
