<?php
// Test simple pour vérifier que les fonctions PDF fonctionnent dans historique.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Inclure le fichier historique pour tester les fonctions
include_once 'historique.php';

echo "<h1>Test des fonctions PDF d'historique.php</h1>";

// Test 1: Vérifier que les classes et fonctions existent
echo "<h2>Test 1: Vérification des classes et fonctions</h2>";

if (class_exists('FPDF_UTF8')) {
    echo "✅ Classe FPDF_UTF8 trouvée<br>";
} else {
    echo "❌ Classe FPDF_UTF8 non trouvée<br>";
}

if (function_exists('generatePDF')) {
    echo "✅ Fonction generatePDF trouvée<br>";
} else {
    echo "❌ Fonction generatePDF non trouvée<br>";
}

if (function_exists('generateSinglePDF')) {
    echo "✅ Fonction generateSinglePDF trouvée<br>";
} else {
    echo "❌ Fonction generateSinglePDF non trouvée<br>";
}

if (function_exists('addCompanyLogo')) {
    echo "✅ Fonction addCompanyLogo trouvée<br>";
} else {
    echo "❌ Fonction addCompanyLogo non trouvée<br>";
}

// Test 2: Vérifier la connexion à la base de données
echo "<h2>Test 2: Connexion à la base de données</h2>";
try {
    $conn = getDbConnection();
    echo "✅ Connexion à la base de données réussie<br>";
    
    // Compter les accusés de réception
    $sql = "SELECT COUNT(*) as total FROM accuses_reception";
    $stmt = $conn->query($sql);
    $result = $stmt->fetch();
    echo "📊 Nombre total d'accusés de réception: " . $result['total'] . "<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur de connexion: " . $e->getMessage() . "<br>";
}

// Test 3: Vérifier la structure des données
echo "<h2>Test 3: Structure des données</h2>";
try {
    $conn = getDbConnection();
    $sql = "SELECT id as ID, collaborateur as Collaborateur, responsable as Responsable,
            date_creation as Date, materiel_remis as Remis, materiel_recu as Recu
            FROM accuses_reception
            ORDER BY date_creation DESC LIMIT 1";
    
    $stmt = $conn->query($sql);
    $ar = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($ar) {
        echo "✅ Données récupérées avec succès<br>";
        echo "📋 Exemple de données:<br>";
        echo "- ID: " . htmlspecialchars($ar['ID']) . "<br>";
        echo "- Collaborateur: " . htmlspecialchars($ar['Collaborateur']) . "<br>";
        echo "- Responsable: " . htmlspecialchars($ar['Responsable']) . "<br>";
        echo "- Date: " . htmlspecialchars($ar['Date']) . "<br>";
    } else {
        echo "⚠️ Aucune donnée trouvée dans la base<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Erreur lors de la récupération des données: " . $e->getMessage() . "<br>";
}

// Test 4: Vérifier que FPDF fonctionne
echo "<h2>Test 4: Test FPDF basique</h2>";
try {
    $pdf = new FPDF_UTF8();
    echo "✅ Création d'instance FPDF_UTF8 réussie<br>";
    
    // Test de conversion de texte UTF-8
    $testText = "Évaluation des caractères accentués: é, è, à, ç, ü";
    echo "✅ Test de texte UTF-8: " . htmlspecialchars($testText) . "<br>";
    
} catch (Exception $e) {
    echo "❌ Erreur FPDF: " . $e->getMessage() . "<br>";
}

echo "<h2>🎉 Tests terminés</h2>";
echo "<p><a href='historique.php'>← Retour à l'historique</a></p>";
?>
