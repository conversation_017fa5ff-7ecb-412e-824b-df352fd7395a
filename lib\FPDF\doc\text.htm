<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Text</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Text</h1>
<code>Text(<b>float</b> x, <b>float</b> y, <b>string</b> txt)</code>
<h2>Description</h2>
Imprime une chaîne de caractères. L'origine est à gauche du premier caractère, sur la ligne
de base.
Cette méthode permet de positionner précisément une chaîne dans la page, mais il est généralement
plus simple d'utiliser Cell(), MultiCell() ou Write() qui sont les méthodes standard pour imprimer
du texte.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
Abscisse de l'origine.
</dd>
<dt><code>y</code></dt>
<dd>
Ordonnée de l'origine.
</dd>
<dt><code>txt</code></dt>
<dd>
Chaîne à imprimer.
</dd>
</dl>
<h2>Voir</h2>
<a href="setfont.htm">SetFont</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="cell.htm">Cell</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="write.htm">Write</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
