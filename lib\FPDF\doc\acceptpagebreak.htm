<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AcceptPageBreak</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>AcceptPageBreak</h1>
<code><b>boolean</b> AcceptPageBreak()</code>
<h2>Description</h2>
Lorsqu'une condition de saut de page est remplie, la méthode est appelée, et en fonction de la
valeur de retour, le saut est effectué ou non. L'implémentation par défaut renvoie une valeur
selon le mode sélectionné par SetAutoPageBreak().
<br>
Cette méthode est appelée automatiquement et ne devrait donc pas être appelée directement par
l'application.
<h2>Exemple</h2>
La méthode est redéfinie dans une classe dérivée afin d'obtenir un formatage sur 3 colonnes :
<div class="doc-source">
<pre><code>class PDF extends FPDF
{
    protected $col = 0;

    function SetCol($col)
    {
        // Positionnement sur une colonne
        $this-&gt;col = $col;
        $x = 10 + $col*65;
        $this-&gt;SetLeftMargin($x);
        $this-&gt;SetX($x);
    }

    function AcceptPageBreak()
    {
        if($this-&gt;col&lt;2)
        {
            // Aller à la colonne suivante
            $this-&gt;SetCol($this-&gt;col+1);
            $this-&gt;SetY(10);
            return false;
        }
        else
        {
            // Retour en première colonne et saut de page
            $this-&gt;SetCol(0);
            return true;
        }
    }
}

$pdf = new PDF();
$pdf-&gt;AddPage();
$pdf-&gt;SetFont('Arial', '', 12);
for($i=1;$i&lt;=300;$i++)
    $pdf-&gt;Cell(0, 5, "Ligne $i", 0, 1);
$pdf-&gt;Output();</code></pre>
</div>
<h2>Voir</h2>
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
