<?php
function readCsv($filename) {
    $rows = [];
    if (file_exists($filename) && ($handle = fopen($filename, 'r')) !== false) {
        while (($data = fgetcsv($handle)) !== false) {
            $rows[] = $data;
        }
        fclose($handle);
    }
    return $rows;
}

function writeCsv($filename, $data, $append = false) {
    $mode = $append ? 'a' : 'w';
    if (($handle = fopen($filename, $mode)) !== false) {
        foreach ($data as $row) {
            fputcsv($handle, $row);
        }
        fclose($handle);
    }
}
?>
