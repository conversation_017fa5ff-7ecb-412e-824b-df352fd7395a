<?php
require_once 'config/database.php';
session_start();

// Vérifier si l'utilisateur est connecté et est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: login.php');
    exit();
}

// Traitement du formulaire d'ajout d'utilisateur
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    $email = $_POST['email'] ?? '';
    $role = $_POST['role'] ?? '';
    $service = $_POST['service'] ?? '';

    if (!empty($username) && !empty($password) && !empty($email) && !empty($role)) {
        // Hasher le mot de passe
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);

        try {
            $stmt = $db->prepare("INSERT INTO users (username, password, email, role, service) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$username, $hashed_password, $email, $role, $service]);
            $success_message = "Utilisateur ajouté avec succès!";
        } catch (PDOException $e) {
            $error_message = "Erreur lors de l'ajout de l'utilisateur: " . $e->getMessage();
        }
    } else {
        $error_message = "Tous les champs obligatoires doivent être remplis.";
    }
}

// Récupérer la liste des utilisateurs
$stmt = $db->query("SELECT * FROM users ORDER BY username");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Administration des utilisateurs</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <?php include 'top-bar-admin.php'; ?>
    
    <div class="container mt-4">
        <h1>Gestion des utilisateurs</h1>
        
        <?php if (isset($success_message)): ?>
            <div class="alert alert-success"><?php echo $success_message; ?></div>
        <?php endif; ?>
        
        <?php if (isset($error_message)): ?>
            <div class="alert alert-danger"><?php echo $error_message; ?></div>
        <?php endif; ?>

        <div class="card mb-4">
            <div class="card-header">
                <h2>Ajouter un nouvel utilisateur</h2>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="username" class="form-label">Nom d'utilisateur*</label>
                            <input type="text" class="form-control" id="username" name="username" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email*</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="password" class="form-label">Mot de passe*</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="col-md-6">
                            <label for="role" class="form-label">Rôle*</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="">Sélectionner un rôle</option>
                                <option value="user">Utilisateur</option>
                                <option value="admin">Administrateur</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="service" class="form-label">Service</label>
                        <select class="form-select" id="service" name="service">
                            <option value="">Sélectionner un service</option>
                            <option value="ADV">ADV</option>
                            <option value="Commerciaux">Commerciaux</option>
                            <option value="Communication">Communication</option>
                            <option value="Finance">Finance</option>
                            <option value="IT">IT</option>
                            <option value="Logistique">Logistique</option>
                            <option value="RH">RH</option>
                            <option value="Service Technique">Service Technique</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-primary">Ajouter l'utilisateur</button>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h2>Liste des utilisateurs</h2>
            </div>
            <div class="card-body">
                <div class="table-responsive">                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="select-all" class="form-check-input">
                                </th>
                                <th>
                                    <a href="?sort=username&order=<?= ($_GET['order'] ?? 'ASC') === 'ASC' ? 'DESC' : 'ASC' ?>">
                                        Nom d'utilisateur 
                                        <?php if(($_GET['sort'] ?? '') === 'username'): ?>
                                            <i class="fas fa-sort-<?= ($_GET['order'] ?? 'ASC') === 'ASC' ? 'up' : 'down' ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>
                                    <a href="?sort=email&order=<?= ($_GET['order'] ?? 'ASC') === 'ASC' ? 'DESC' : 'ASC' ?>">
                                        Email
                                        <?php if(($_GET['sort'] ?? '') === 'email'): ?>
                                            <i class="fas fa-sort-<?= ($_GET['order'] ?? 'ASC') === 'ASC' ? 'up' : 'down' ?>"></i>
                                        <?php endif; ?>
                                    </a>
                                </th>
                                <th>Rôle</th>                                <th>Service</th>
                                <th>Statut</th>
                                <th>Dernière connexion</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="user_ids[]" value="<?= $user['id'] ?>" class="form-check-input user-select">
                                </td>
                                <td><?php echo htmlspecialchars($user['username']); ?></td>
                                <td><?php echo htmlspecialchars($user['email']); ?></td>
                                <td>
                                    <span class="badge bg-<?= $user['role'] === 'admin' ? 'danger' : 'primary' ?>">
                                        <?php echo htmlspecialchars($user['role']); ?>
                                    </span>
                                </td>
                                <td><?php echo htmlspecialchars($user['service']); ?></td>
                                <td>
                                    <span class="badge bg-<?= ($user['status'] ?? 'active') === 'active' ? 'success' : 'warning' ?>">
                                        <?php echo htmlspecialchars($user['status'] ?? 'active'); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php 
                                    if ($user['last_login']) {
                                        echo date('d/m/Y H:i', strtotime($user['last_login']));
                                    } else {
                                        echo '<span class="text-muted">Jamais</span>';                                    }
                                    ?>
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-sm btn-primary" onclick="editUser(<?= $user['id'] ?>)">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-warning" onclick="resetPassword(<?= $user['id'] ?>)">
                                            <i class="fas fa-key"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-danger" onclick="deleteUser(<?= $user['id'] ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <script>
        // Gestion de la sélection multiple
        document.getElementById('select-all').addEventListener('change', function() {
            document.querySelectorAll('.user-select').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });

        // Gestion des actions en masse
        document.querySelector('select[name="action"]').addEventListener('change', function() {
            document.querySelectorAll('.action-option').forEach(option => {
                option.style.display = 'none';
            });

            if (this.value === 'change_role') {
                document.getElementById('role-option').style.display = 'block';
            } else if (this.value === 'change_status') {
                document.getElementById('status-option').style.display = 'block';
            }
        });

        // Confirmation des actions
        document.getElementById('bulk-actions-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const selectedUsers = document.querySelectorAll('.user-select:checked').length;
            if (selectedUsers === 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: 'Veuillez sélectionner au moins un utilisateur'
                });
                return;
            }

            const action = document.querySelector('select[name="action"]').value;
            let message = '';
            switch (action) {
                case 'delete':
                    message = 'Êtes-vous sûr de vouloir supprimer ces utilisateurs ?';
                    break;
                case 'change_role':
                    message = 'Êtes-vous sûr de vouloir modifier le rôle de ces utilisateurs ?';
                    break;
                case 'change_status':
                    message = 'Êtes-vous sûr de vouloir modifier le statut de ces utilisateurs ?';
                    break;
            }

            Swal.fire({
                title: 'Confirmation',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Oui',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    this.submit();
                }
            });
        });

        // Fonctions pour les actions individuelles
        function editUser(id) {
            // À implémenter : modal d'édition
            Swal.fire({
                title: 'Modification utilisateur',
                text: 'Fonctionnalité en cours de développement',
                icon: 'info'
            });
        }        function resetPassword(id) {
            Swal.fire({
                title: 'Réinitialiser le mot de passe',
                text: 'Êtes-vous sûr de vouloir réinitialiser le mot de passe de cet utilisateur ?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Oui',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Appel AJAX pour réinitialiser le mot de passe
                    fetch('reset_password.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `user_id=${id}`
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            Swal.fire({
                                title: 'Succès',
                                html: `Le mot de passe a été réinitialisé.<br><br>
                                      <strong>Nouveau mot de passe :</strong><br>
                                      <code>${data.new_password}</code><br><br>
                                      Veuillez communiquer ce mot de passe à l'utilisateur de manière sécurisée.`,
                                icon: 'success'
                            });
                        } else {
                            Swal.fire({
                                title: 'Erreur',
                                text: data.error || 'Une erreur est survenue',
                                icon: 'error'
                            });
                        }
                    })
                    .catch(error => {
                        Swal.fire({
                            title: 'Erreur',
                            text: 'Une erreur est survenue lors de la réinitialisation',
                            icon: 'error'
                        });
                    });
                }
            });
        }

        function deleteUser(id) {
            Swal.fire({
                title: 'Supprimer l\'utilisateur',
                text: 'Êtes-vous sûr de vouloir supprimer cet utilisateur ?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'Oui',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    // À implémenter : appel AJAX pour supprimer l'utilisateur
                    window.location.href = `admin.php?action=delete&user_id=${id}`;
                }
            });
        }
    </script>
</body>
</html>