<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetXY</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetXY</h1>
<code>SetXY(<b>float</b> x, <b>float</b> y)</code>
<h2>Description</h2>
Fixe l'abscisse et l'ordonnée de la position courante. Si les valeurs transmises sont négatives,
elles sont relatives respectivement aux extrémités droite et basse de la page.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
La valeur de l'abscisse.
</dd>
<dt><code>y</code></dt>
<dd>
La valeur de l'ordonnée.
</dd>
</dl>
<h2>Voir</h2>
<a href="setx.htm">SetX</a>,
<a href="sety.htm">SetY</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
