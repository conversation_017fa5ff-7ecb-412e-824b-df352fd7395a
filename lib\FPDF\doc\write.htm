<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Write</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Write</h1>
<code>Write(<b>float</b> h, <b>string</b> txt [, <b>mixed</b> link])</code>
<h2>Description</h2>
Cette méthode imprime du texte à partir de la position courante. Lorsque la marge droite est
atteinte (ou que le caractère \n est rencontré), un saut de ligne est effectué et le texte
continue à partir de la marge gauche. Au retour de la méthode, la position courante est située
juste à la fin du texte.
<br>
Il est possible de mettre un lien sur le texte.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>h</code></dt>
<dd>
Hauteur de la ligne.
</dd>
<dt><code>txt</code></dt>
<dd>
Chaîne à imprimer.
</dd>
<dt><code>link</code></dt>
<dd>
URL ou identifiant retourné par AddLink().
</dd>
</dl>
<h2>Exemple</h2>
<div class="doc-source">
<pre><code>// Début en police normale
$pdf-&gt;SetFont('Arial', '', 14);
$pdf-&gt;Write(5, 'Visitez ');
// Lien en bleu souligné
$pdf-&gt;SetTextColor(0, 0, 255);
$pdf-&gt;SetFont('', 'U');
$pdf-&gt;Write(5, 'www.fpdf.org', 'http://www.fpdf.org');</code></pre>
</div>
<h2>Voir</h2>
<a href="setfont.htm">SetFont</a>,
<a href="settextcolor.htm">SetTextColor</a>,
<a href="addlink.htm">AddLink</a>,
<a href="multicell.htm">MultiCell</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
