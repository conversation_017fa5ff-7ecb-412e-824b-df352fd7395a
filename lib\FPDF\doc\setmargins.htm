<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>SetMargins</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetMargins</h1>
<code>SetMargins(<b>float</b> left, <b>float</b> top [, <b>float</b> right])</code>
<h2>Description</h2>
Fixe les marges gauche, haute et droite. Par défaut, elles valent 1 cm. Appelez cette méthode si
vous désirez les changer.
<h2>Paramètres</h2>
<dl class="param">
<dt><code>left</code></dt>
<dd>
Marge gauche.
</dd>
<dt><code>top</code></dt>
<dd>
Marge haute.
</dd>
<dt><code>right</code></dt>
<dd>
Marge droite. Par défaut, est égale à la gauche.
</dd>
</dl>
<h2>Voir</h2>
<a href="setleftmargin.htm">SetLeftMargin</a>,
<a href="settopmargin.htm">SetTopMargin</a>,
<a href="setrightmargin.htm">SetRightMargin</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
