# Corrections UTF-8 pour les Accusés de Réception PDF

## Problème résolu

Les caractères spéciaux français (é, è, à, ç, etc.) ne s'affichaient pas correctement dans les fichiers PDF générés par le script `accuse_reception.php`.

## Modifications apportées

### 1. Classe FPDF_UTF8 étendue

Création d'une nouvelle classe `FPDF_UTF8` qui étend la classe FPDF standard pour supporter l'encodage UTF-8 :

- **Tableau de conversion** : Mapping des caractères UTF-8 vers ISO-8859-1
- **Méthode convertText()** : Conversion automatique des textes UTF-8
- **Méthodes surchargées** : Cell(), MultiCell(), Write() pour traiter automatiquement l'encodage

### 2. Configuration de la base de données

Modification de la fonction `getDbConnection()` :
- Ajout de `charset=utf8` dans la chaîne de connexion PDO
- Exécution de `SET NAMES utf8` pour forcer l'encodage UTF-8
- Configuration de `PDO::ATTR_DEFAULT_FETCH_MODE`

### 3. Configuration PHP

Ajout au début du script :
```php
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');
```

### 4. Correction des fonctions PDF

- Remplacement de `new FPDF()` par `new FPDF_UTF8()`
- Suppression de `htmlspecialchars()` dans les fonctions PDF (remplacé par `cleanText()`)
- Correction des textes français : "Accusé de réception", "matériel", "reçu", etc.

### 5. Amélioration de htmlspecialchars()

Ajout de l'encodage UTF-8 explicite :
```php
htmlspecialchars($text, ENT_QUOTES, 'UTF-8')
```

## Caractères supportés

La solution supporte tous les caractères français courants :
- Voyelles accentuées : à, á, â, ã, ä, å, è, é, ê, ë, ì, í, î, ï, ò, ó, ô, õ, ö, ù, ú, û, ü
- Consonnes spéciales : ç, ñ
- Majuscules accentuées : À, Á, Â, Ã, Ä, Å, È, É, Ê, Ë, Ì, Í, Î, Ï, Ò, Ó, Ô, Õ, Ö, Ù, Ú, Û, Ü, Ç, Ñ
- Symboles spéciaux : €, £, ¥, §, ©, ®, °, ±, ², ³, µ, ¶, ¼, ½, ¾

## Fichiers de test

Trois fichiers de test ont été créés pour valider la solution :

1. **test_pdf_utf8.php** : Test général des caractères français
2. **test_insert_data.php** : Insertion de données de test avec caractères français
3. **test_pdf_generation.php** : Test de génération PDF avec données réelles

## Utilisation

La solution est maintenant intégrée dans `accuse_reception.php`. Tous les PDFs générés afficheront correctement les caractères français sans modification supplémentaire nécessaire.

## Compatibilité

- Compatible avec PHP 7.4+
- Nécessite l'extension `mbstring`
- Fonctionne avec MySQL/MariaDB en UTF-8
- Compatible avec FPDF 1.86

## Tests effectués

✅ Affichage correct des caractères français dans les PDFs
✅ Sauvegarde et récupération correctes depuis la base de données
✅ Affichage correct dans l'interface web
✅ Génération de PDFs individuels et multiples
✅ Compatibilité avec les données existantes

## Notes techniques

La conversion UTF-8 vers ISO-8859-1 est nécessaire car FPDF utilise par défaut l'encodage WinAnsiEncoding qui correspond à ISO-8859-1. La classe FPDF_UTF8 effectue cette conversion de manière transparente tout en préservant tous les caractères français.
