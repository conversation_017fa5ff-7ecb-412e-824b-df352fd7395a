<?php
// Test de la suppression silencieuse
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='fr'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<title>Test Suppression Silencieuse</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".success { background-color: #d4edda; color: #155724; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".info { background-color: #d1ecf1; color: #0c5460; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo ".warning { background-color: #fff3cd; color: #856404; padding: 15px; border-radius: 4px; margin: 10px 0; }";
echo "table { border-collapse: collapse; width: 100%; margin: 20px 0; }";
echo "th, td { border: 1px solid #ddd; padding: 12px; text-align: left; }";
echo "th { background-color: #f8f9fa; }";
echo ".btn { padding: 8px 12px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px; }";
echo ".btn-danger { background-color: #dc3545; color: white; }";
echo ".btn-primary { background-color: #007bff; color: white; }";
echo ".btn:hover { opacity: 0.8; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔇 Test de Suppression Silencieuse</h1>";

// Fonction de connexion à la base de données
function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        echo "<div class='error'>❌ Erreur de connexion : " . $e->getMessage() . "</div>";
        return null;
    }
}

// Vérifier les modifications apportées
function checkModifications() {
    echo "<h2>🔍 Vérification des Modifications</h2>";
    
    $fileContent = file_get_contents('accuse_reception.php');
    
    $checks = [
        "Redirection silencieuse implémentée" => strpos($fileContent, 'header("Location: accuse_reception.php");') !== false,
        "Message de feedback supprimé" => strpos($fileContent, '<!-- Messages de feedback supprimés') !== false,
        "Styles CSS commentés" => strpos($fileContent, '/* Styles pour les messages supprimés') !== false,
        "Pas de paramètre 'deleted' dans l'URL" => strpos($fileContent, '?deleted=1&msg=') === false,
        "Pas de variable \$message utilisée" => strpos($fileContent, '$message = $result[\'message\'];') === false
    ];
    
    foreach ($checks as $checkName => $result) {
        $status = $result ? "✅" : "❌";
        $class = $result ? "success" : "warning";
        echo "<div class='$class'>$status $checkName</div>";
    }
}

// Afficher les enregistrements pour test
function displayRecordsForTest() {
    echo "<h2>📋 Enregistrements Disponibles pour Test</h2>";
    
    $conn = getDbConnection();
    if (!$conn) return;
    
    try {
        $sql = "SELECT id, collaborateur, responsable, date_creation FROM accuses_reception ORDER BY date_creation DESC LIMIT 5";
        $stmt = $conn->query($sql);
        $records = $stmt->fetchAll();
        
        if (empty($records)) {
            echo "<div class='info'>ℹ️ Aucun enregistrement trouvé pour le test.</div>";
            echo "<p><a href='test_insert_data.php' class='btn btn-primary'>Créer des données de test</a></p>";
            return;
        }
        
        echo "<div class='info'>";
        echo "<strong>Instructions de test :</strong><br>";
        echo "1. Cliquez sur un bouton 'Supprimer' ci-dessous<br>";
        echo "2. Confirmez la suppression<br>";
        echo "3. Vérifiez qu'AUCUN message ne s'affiche après la suppression<br>";
        echo "4. Vérifiez que l'enregistrement a bien été supprimé de la liste<br>";
        echo "</div>";
        
        echo "<table>";
        echo "<tr><th>ID</th><th>Collaborateur</th><th>Responsable</th><th>Date</th><th>Action</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . $record['id'] . "</td>";
            echo "<td>" . htmlspecialchars($record['collaborateur']) . "</td>";
            echo "<td>" . htmlspecialchars($record['responsable']) . "</td>";
            echo "<td>" . $record['date_creation'] . "</td>";
            echo "<td>";
            echo "<a href='accuse_reception.php?delete_id=" . $record['id'] . "' class='btn btn-danger' onclick='return confirm(\"Supprimer l\\\"accusé de réception de " . htmlspecialchars($record['collaborateur']) . " ? (Test suppression silencieuse)\")'>🗑️ Supprimer (Test)</a>";
            echo "</td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<div class='error'>❌ Erreur lors de la récupération des données: " . $e->getMessage() . "</div>";
    }
}

// Simuler une suppression pour vérifier le comportement
function simulateDeleteBehavior() {
    echo "<h2>🎭 Simulation du Comportement de Suppression</h2>";
    
    echo "<div class='info'>";
    echo "<strong>Comportement AVANT les modifications :</strong><br>";
    echo "1. Clic sur supprimer → Confirmation → Suppression<br>";
    echo "2. Redirection vers : <code>accuse_reception.php?deleted=1&msg=Accusé+de+réception+supprimé</code><br>";
    echo "3. Affichage d'un message vert : \"Accusé de réception de [Nom] supprimé avec succès\"<br>";
    echo "</div>";
    
    echo "<div class='success'>";
    echo "<strong>Comportement APRÈS les modifications :</strong><br>";
    echo "1. Clic sur supprimer → Confirmation → Suppression<br>";
    echo "2. Redirection vers : <code>accuse_reception.php</code> (sans paramètres)<br>";
    echo "3. <strong>AUCUN message affiché</strong> - Suppression silencieuse ✅<br>";
    echo "</div>";
}

// Afficher les informations de test
checkModifications();
simulateDeleteBehavior();
displayRecordsForTest();

echo "<hr>";
echo "<div class='info'>";
echo "<h3>🎯 Points de Vérification :</h3>";
echo "<ul>";
echo "<li>✅ La suppression fonctionne toujours (l'enregistrement disparaît)</li>";
echo "<li>✅ Aucun message de confirmation ne s'affiche</li>";
echo "<li>✅ L'URL reste propre (pas de paramètres 'deleted' ou 'msg')</li>";
echo "<li>✅ Les autres fonctionnalités (ajout, PDF) ne sont pas affectées</li>";
echo "<li>✅ La confirmation JavaScript fonctionne toujours</li>";
echo "</ul>";
echo "</div>";

echo "<div style='margin-top: 30px;'>";
echo "<a href='accuse_reception.php' class='btn btn-primary'>🔙 Retour à la page principale</a>";
echo " ";
echo "<a href='test_insert_data.php' class='btn btn-primary'>➕ Créer des données de test</a>";
echo "</div>";

echo "</body></html>";
?>
