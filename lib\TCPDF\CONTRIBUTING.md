# How to Contribute


## Reporting a bug

* **Do not open up a GitHub issue if the bug is a security vulnerability**, and instead to refer to our [Security policy](SECURITY.md).

* Ensure the bug was not already reported by searching on GitHub Issues.

* If you're unable to find an open issue addressing the problem, open a new one. Be sure to include a **title and clear description**, as much relevant information as possible, and a **code sample** or an **executable test case** demonstrating the expected behavior that is not occurring.


## Submitting a bug fix

* Open a new GitHub pull request with the patch.

* Ensure the PR description clearly describes the problem and solution. Include the relevant issue number if applicable.

* Ensure the new code is following the existing conventions and the unit test coverage is 100%.

* Before submitting, please run the following command locally to ensure the code is passing the automatic checks: `make buildall`.


## Add a new feature or change an existing one

* Before writing any code please suggest the change by opening a new Feature Request on Issues.


## Running Tests Locally

To ensure your changes do not break existing functionality, run the following command to execute all tests:

```bash
make test
```

Ensure that all tests pass before submitting your pull request.
