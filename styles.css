:root {
    --primary-color: #333;
    --hover-color: #f57c00;
    --text-color: white;
}

/* Styles de la barre de navigation */
nav {
    background-color: var(--primary-color);
    padding: 15px;
    text-align: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    margin: 0;
    padding: 0;
}

nav ul li {
    margin: 0 15px;
}

nav ul li a {
    color: var(--text-color);
    text-decoration: none;
    font-size: 1.1em;
    padding: 10px 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

nav ul li a:hover {
    background-color: var(--hover-color);
    transform: scale(1.05);
}

nav ul li a.active {
    background-color: var(--hover-color);
    font-weight: bold;
}

nav ul li a i {
    font-size: 1.2em;
}

nav ul li a:focus {
    outline: 2px dashed var(--hover-color);
    outline-offset: 4px;
}

#backToTop {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: var(--hover-color);
    color: var(--text-color);
    border: none;
    padding: 10px 15px;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: background-color 0.3s ease;
}

#backToTop:hover {
    background-color: var(--primary-color);
}

/* Global styles */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f9f9f9;
    color: #222;
    transition: background-color 0.3s ease, color 0.3s ease;
}

header, nav {
    background-color: #005ea2;
    color: white;
    padding: 15px;
    text-align: center;
}

header ul, nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    padding: 0;
}

header ul li, nav ul li {
    margin: 0 15px;
}

header ul li a, nav ul li a {
    color: white;
    text-decoration: none;
    font-size: 1.1em;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

header ul li a:hover, nav ul li a:hover {
    background-color: #ffa500;
}

.container {
    max-width: 90%;
    margin: 100px auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

button {
    padding: 10px 20px;
    background-color: #f57c00;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #e67e22;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

th, td {
    padding: 10px;
    border: 1px solid #ddd;
    text-align: left;
}

th {
    background-color: #f57c00;
    color: white;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

tr:hover {
    background-color: #f1f1f1;
}

footer {
    background-color: #005ea2;
    color: white;
    text-align: center;
    padding: 20px;
    margin-top: 30px;
    width: 100%;
}

footer button {
    background-color: #ffa500;
    color: #222;
}

footer button:hover {
    background-color: #e67e22;
}

/* Navbar styles */
.navbar {
    background-color: #005ea2;
    color: white;
    padding: 10px 20px;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.navbar-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.navbar-logo {
    font-size: 1.5em;
    font-weight: bold;
    color: white;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 10px;
}

.navbar-menu {
    list-style: none;
    display: flex;
    gap: 20px;
    margin: 0;
    padding: 0;
}

.navbar-menu li {
    position: relative;
}

.navbar-menu li a {
    color: white;
    text-decoration: none;
    font-size: 1em;
    padding: 10px 15px;
    border-radius: 5px;
    transition: background-color 0.3s, transform 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.navbar-menu li a:hover {
    background-color: #ffa500;
    transform: scale(1.05);
}

.navbar-menu li a.active {
    background-color: #ffa500;
    font-weight: bold;
}

.navbar-menu li a i {
    font-size: 1.2em;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
    }

    nav ul li {
        margin: 10px 0;
    }

    .navbar-container {
        flex-direction: column;
        align-items: flex-start;
    }

    .navbar-menu {
        flex-direction: column;
        width: 100%;
    }

    .navbar-menu li {
        width: 100%;
    }

    .navbar-menu li a {
        width: 100%;
        text-align: left;
    }
}

/* Styles de la barre supérieure */
.top-bar {
    background-color: #333;
    padding: 15px;
    text-align: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 1000;
}

.top-bar a {
    color: white;
    text-decoration: none;
    margin: 0 15px;
    font-size: 1.1em;
    font-weight: bold;
    transition: color 0.3s;
}

.top-bar a:hover {
    color: #ffa500;
}
