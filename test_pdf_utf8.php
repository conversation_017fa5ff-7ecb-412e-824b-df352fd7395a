<?php
// Test pour vérifier l'affichage des caractères français dans le PDF
mb_internal_encoding('UTF-8');
mb_http_output('UTF-8');

require_once('lib/FPDF/fpdf.php');

// Classe FPDF étendue pour supporter l'UTF-8 (copie de celle dans accuse_reception.php)
class FPDF_UTF8 extends FPDF
{
    // Tableau de conversion des caractères UTF-8 vers ISO-8859-1
    private $utf8_to_iso = array(
        'à' => 'à', 'á' => 'á', 'â' => 'â', 'ã' => 'ã', 'ä' => 'ä', 'å' => 'å',
        'è' => 'è', 'é' => 'é', 'ê' => 'ê', 'ë' => 'ë',
        'ì' => 'ì', 'í' => 'í', 'î' => 'î', 'ï' => 'ï',
        'ò' => 'ò', 'ó' => 'ó', 'ô' => 'ô', 'õ' => 'õ', 'ö' => 'ö',
        'ù' => 'ù', 'ú' => 'ú', 'û' => 'û', 'ü' => 'ü',
        'ç' => 'ç', 'ñ' => 'ñ',
        'À' => 'À', 'Á' => 'Á', 'Â' => 'Â', 'Ã' => 'Ã', 'Ä' => 'Ä', 'Å' => 'Å',
        'È' => 'È', 'É' => 'É', 'Ê' => 'Ê', 'Ë' => 'Ë',
        'Ì' => 'Ì', 'Í' => 'Í', 'Î' => 'Î', 'Ï' => 'Ï',
        'Ò' => 'Ò', 'Ó' => 'Ó', 'Ô' => 'Ô', 'Õ' => 'Õ', 'Ö' => 'Ö',
        'Ù' => 'Ù', 'Ú' => 'Ú', 'Û' => 'Û', 'Ü' => 'Ü',
        'Ç' => 'Ç', 'Ñ' => 'Ñ',
        '€' => '€', '£' => '£', '¥' => '¥', '§' => '§', '©' => '©', '®' => '®',
        '°' => '°', '±' => '±', '²' => '²', '³' => '³', 'µ' => 'µ', '¶' => '¶',
        '¼' => '¼', '½' => '½', '¾' => '¾'
    );
    
    private function convertText($txt)
    {
        // Si le texte est vide, le retourner tel quel
        if (empty($txt)) {
            return $txt;
        }

        // Si le texte est en UTF-8, le convertir
        if (mb_check_encoding($txt, 'UTF-8')) {
            // Utiliser iconv si disponible (plus fiable pour FPDF)
            if (function_exists('iconv')) {
                $converted = iconv('UTF-8', 'ISO-8859-1//IGNORE', $txt);
                if ($converted !== false) {
                    return $converted;
                }
            }

            // Fallback: utiliser notre tableau de correspondance
            $converted = strtr($txt, $this->utf8_to_iso);

            // Si ça ne marche toujours pas, essayer mb_convert_encoding
            if ($converted === $txt) {
                $converted = mb_convert_encoding($txt, 'ISO-8859-1', 'UTF-8');
            }

            return $converted;
        }

        return $txt;
    }
    
    function Cell($w, $h=0, $txt='', $border=0, $ln=0, $align='', $fill=false, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Cell($w, $h, $txt, $border, $ln, $align, $fill, $link);
    }
    
    function MultiCell($w, $h, $txt, $border=0, $align='J', $fill=false)
    {
        $txt = $this->convertText($txt);
        parent::MultiCell($w, $h, $txt, $border, $align, $fill);
    }
    
    function Write($h, $txt, $link='')
    {
        $txt = $this->convertText($txt);
        parent::Write($h, $txt, $link);
    }
}

// Créer un PDF de test
$pdf = new FPDF_UTF8();
$pdf->AddPage();
$pdf->SetFont('Arial', 'B', 16);

// Tester différents caractères français
$pdf->Cell(0, 10, 'Test des caractères français', 0, 1, 'C');
$pdf->Ln(10);

$pdf->SetFont('Arial', '', 12);
$pdf->Cell(0, 10, 'Voyelles accentuées: à é è ê ë î ï ô ù û ü ç', 0, 1);
$pdf->Cell(0, 10, 'Majuscules: À É È Ê Ë Î Ï Ô Ù Û Ü Ç', 0, 1);
$pdf->Cell(0, 10, 'Mots courants: français, réception, matériel, créé', 0, 1);
$pdf->Cell(0, 10, 'Phrases: Je soussigné atteste avoir reçu le matériel.', 0, 1);

$pdf->Ln(10);
$pdf->MultiCell(0, 10, 'Test MultiCell: Ceci est un test pour vérifier que les caractères spéciaux français (à, é, è, ê, ë, î, ï, ô, ù, û, ü, ç) s\'affichent correctement dans un PDF généré avec FPDF. Les accents doivent être visibles et lisibles.');

// Générer le PDF
$pdf->Output('F', 'test_caracteres_francais.pdf');

echo "PDF de test généré: test_caracteres_francais.pdf<br>";
echo "Vérifiez que tous les caractères français s'affichent correctement.";
?>
