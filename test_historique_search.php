<?php
// Test de la fonction de recherche dans historique.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function addTestData() {
    $conn = getDbConnection();
    
    // Données de test avec caractères français
    $testData = [
        ['<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '2024-01-15', 'Ordinateur portable HP, souris', 'Ancien PC Dell'],
        ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '2024-01-20', '<PERSON><PERSON>ran 24", clavier AZERTY', '<PERSON><PERSON><PERSON> 19" défaillant'],
        ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '2024-02-01', 'T<PERSON>léphone IP, casque', 'Ancien téléphone analogique'],
        ['Élise Château', '<PERSON> <PERSON>', '2024-02-10', 'Tablette iPad, stylet', 'Tablette Android obsolète'],
        ['José García', 'Marie-Claire Dubois', '2024-02-15', 'Imprimante laser, câbles', 'Imprimante jet d\'encre'],
        ['Anaïs Lefèvre', 'Cédric Beaumont', '2024-03-01', 'Ordinateur fixe, écran', 'PC portable en panne']
    ];
    
    // Vider les données existantes pour le test
    $conn->exec("DELETE FROM accuses_reception WHERE collaborateur LIKE '%Test%' OR collaborateur IN ('François Müller', 'Marie-Claire Dubois', 'Cédric Beaumont', 'Élise Château', 'José García', 'Anaïs Lefèvre')");
    
    // Insérer les données de test
    $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu) VALUES (?, ?, ?, ?, ?)";
    $stmt = $conn->prepare($sql);
    
    foreach ($testData as $data) {
        $stmt->execute($data);
    }
    
    return count($testData);
}

function testSearch($searchTerm) {
    $conn = getDbConnection();
    
    $sql = "SELECT collaborateur, responsable, date_creation 
            FROM accuses_reception 
            WHERE (collaborateur LIKE :search1 OR responsable LIKE :search2)
            ORDER BY date_creation DESC";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([
        ':search1' => '%' . $searchTerm . '%',
        ':search2' => '%' . $searchTerm . '%'
    ]);
    
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de Recherche - Historique</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .info { background-color: #e8f4fd; border-color: #bee5eb; color: #0c5460; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f57c00; color: white; }
        .btn { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .highlight { background-color: #fff3cd; padding: 2px 4px; border-radius: 3px; font-weight: bold; }
    </style>
</head>
<body>
<div class="container">
    <h1>🧪 Test de la Fonction de Recherche - Historique</h1>
    
    <?php if (isset($_POST['add_test_data'])): ?>
        <div class="test-section success">
            <h3>✅ Données de test ajoutées</h3>
            <?php 
            $count = addTestData();
            echo "<p><strong>$count</strong> accusés de réception de test ont été ajoutés avec des caractères français.</p>";
            ?>
        </div>
    <?php endif; ?>
    
    <div class="test-section info">
        <h3>📋 Étape 1 : Préparer les données de test</h3>
        <p>Cliquez pour ajouter des données de test avec des caractères français (é, è, à, ç, etc.)</p>
        <form method="POST">
            <button type="submit" name="add_test_data" class="btn btn-success">
                🔄 Ajouter/Recharger les données de test
            </button>
        </form>
    </div>
    
    <div class="test-section info">
        <h3>🔍 Étape 2 : Tests de recherche automatiques</h3>
        
        <?php
        $searchTests = [
            'François' => 'Recherche par prénom avec accent',
            'Müller' => 'Recherche avec caractère spécial (ü)',
            'Marie-Claire' => 'Recherche avec trait d\'union',
            'Lefèvre' => 'Recherche avec accent grave',
            'García' => 'Recherche avec accent aigu',
            'martin' => 'Recherche insensible à la casse',
            'dubois' => 'Recherche en minuscules',
            'BEAUMONT' => 'Recherche en majuscules'
        ];
        
        foreach ($searchTests as $term => $description) {
            echo "<div style='margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 4px;'>";
            echo "<h4>Test : $description</h4>";
            echo "<p><strong>Terme recherché :</strong> \"$term\"</p>";
            
            $results = testSearch($term);
            
            if (empty($results)) {
                echo "<p style='color: #dc3545;'>❌ Aucun résultat trouvé</p>";
            } else {
                echo "<p style='color: #28a745;'>✅ " . count($results) . " résultat(s) trouvé(s)</p>";
                echo "<table>";
                echo "<tr><th>Collaborateur</th><th>Responsable</th><th>Date</th></tr>";
                foreach ($results as $result) {
                    $collaborateur = htmlspecialchars($result['collaborateur'], ENT_QUOTES, 'UTF-8');
                    $responsable = htmlspecialchars($result['responsable'], ENT_QUOTES, 'UTF-8');
                    
                    // Mise en évidence du terme recherché
                    $pattern = '/(' . preg_quote($term, '/') . ')/i';
                    $collaborateur = preg_replace($pattern, '<span class="highlight">$1</span>', $collaborateur);
                    $responsable = preg_replace($pattern, '<span class="highlight">$1</span>', $responsable);
                    
                    echo "<tr>";
                    echo "<td>$collaborateur</td>";
                    echo "<td>$responsable</td>";
                    echo "<td>" . htmlspecialchars($result['date_creation'], ENT_QUOTES, 'UTF-8') . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
            echo "</div>";
        }
        ?>
    </div>
    
    <div class="test-section success">
        <h3>🎯 Étape 3 : Tester l'interface réelle</h3>
        <p>Maintenant que les données de test sont prêtes, testez l'interface de recherche :</p>
        <a href="historique.php" class="btn btn-primary" target="_blank">
            🔗 Ouvrir historique.php
        </a>
        <p style="margin-top: 10px;"><small>
            <strong>Suggestions de tests :</strong><br>
            • Recherchez "François" pour tester les accents<br>
            • Recherchez "Müller" pour tester les caractères spéciaux<br>
            • Recherchez "marie" pour tester la casse<br>
            • Recherchez "xyz" pour tester l'absence de résultats
        </small></p>
    </div>
    
    <div class="test-section warning">
        <h3>⚠️ Nettoyage</h3>
        <p>Pour nettoyer les données de test après vos tests :</p>
        <form method="POST" onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer les données de test ?')">
            <button type="submit" name="clean_test_data" class="btn btn-info">
                🧹 Nettoyer les données de test
            </button>
        </form>
        
        <?php if (isset($_POST['clean_test_data'])): ?>
            <?php
            $conn = getDbConnection();
            $conn->exec("DELETE FROM accuses_reception WHERE collaborateur IN ('François Müller', 'Marie-Claire Dubois', 'Cédric Beaumont', 'Élise Château', 'José García', 'Anaïs Lefèvre')");
            echo "<p style='color: #28a745; margin-top: 10px;'>✅ Données de test supprimées</p>";
            ?>
        <?php endif; ?>
    </div>
</div>
</body>
</html>
