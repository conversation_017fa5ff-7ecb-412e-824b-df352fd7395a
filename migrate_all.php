<?php
require_once __DIR__ . '/config/database.php';

function migrateCsvToDb($csvFile, $tableName, $columns) {
    global $db;
    
    $csvFile = __DIR__ . '/' . $csvFile;
    if (file_exists($csvFile) && ($handle = fopen($csvFile, "r")) !== FALSE) {
        // Skip header row
        fgetcsv($handle, 1000, ",");
        
        while (($data = fgetcsv($handle, 1000, ",")) !== FALSE) {
            $placeholders = str_repeat('?,', count($columns) - 1) . '?';
            $sql = "INSERT INTO $tableName (" . implode(',', $columns) . ") VALUES ($placeholders)";
            
            try {
                $stmt = $db->prepare($sql);
                $stmt->execute($data);
                echo "Ligne ajoutée dans $tableName\n";
            } catch(PDOException $e) {
                echo "Erreur lors de l'insertion dans $tableName : " . $e->getMessage() . "\n";
            }
        }
        fclose($handle);
        echo "Migration terminée pour $tableName\n\n";
    } else {
        echo "Impossible d'ouvrir le fichier $csvFile\n";
    }
}

try {
    // Migration des accusés de réception
    migrateCsvToDb(
        "accuses_reception.csv",
        "accuses_reception",
        ['collaborateur', 'responsable', 'date_creation', 'materiel_remis', 'materiel_recu']
    );

    // Migration des évaluations
    migrateCsvToDb(
        "evaluations.csv",
        "evaluations",
        ['prestataire', 'service', 'qualite', 'delai', 'communication', 'commentaires']
    );

    // Migration des changements de poste
    migrateCsvToDb(
        "changements_de_poste.csv",
        "changements_poste",
        ['nom_utilisateur', 'ancien_poste', 'nouveau_poste', 'date_changement', 'lifecycle', 'responsable', 'type']
    );

    echo "Migration terminée avec succès!";
} catch (Exception $e) {
    echo "Erreur : " . $e->getMessage();
}
