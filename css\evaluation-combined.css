/* Variables CSS pour une meilleure maintenance */
:root {
    --primary-color: #005ea2;
    --secondary-color: #f57c00;
    --danger-color: #dc3545;
    --success-color: #28a745;
    --text-color: #333;
    --border-radius: 8px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #f5f7fa;
}

/* Layout principal */
.evaluation-container {
    max-width: 1400px;
    margin: 80px auto 20px;
    padding: 0 20px;
}

.evaluation-header {
    background: linear-gradient(135deg, #005ea2, #f57c00);
    color: white;
    padding: 40px 20px;
    border-radius: 8px;
    margin-bottom: 30px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.evaluation-header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
}

.evaluation-header p {
    font-size: 1.2em;
    opacity: 0.9;
}

.evaluation-grid {
    display: grid;
    grid-template-columns: minmax(300px, 2fr) minmax(400px, 3fr);
    gap: clamp(20px, 3vw, 40px);
    margin-top: clamp(20px, 3vh, 40px);
}

@media (max-width: 1024px) {
    .evaluation-grid {
        grid-template-columns: 1fr;
    }
}

.providers-list {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.provider-card {
    background: #f8f9fa;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    border-left: 4px solid #f57c00;
    transition: all 0.3s ease;
}

.provider-card:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.evaluation-form {
    background: white;
    padding: 25px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1em;
}

.rating-group {
    display: flex;
    gap: 10px;
    margin: 10px 0;
}

.rating-option {
    flex: 1;
    text-align: center;
}

.rating-option input[type="radio"] {
    display: none;
}

.rating-option label {
    display: block;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rating-option input[type="radio"]:checked + label {
    background: #f57c00;
    color: white;
}

.submit-btn {
    background: #f57c00;
    color: white;
    padding: 12px 25px;
    border: none;
    border-radius: 4px;
    font-size: 1.1em;
    cursor: pointer;
    transition: background 0.3s ease;
}

.submit-btn:hover {
    background: #e65100;
}

.statistics-section {
    margin-top: 30px;
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-container {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
}

.feedback-message {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.feedback-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.feedback-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.no-data-message {
    text-align: center;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    color: #6c757d;
}

.no-data-message i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.no-data-message p {
    margin: 0.5rem 0;
    font-size: 1rem;
}
