<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
	bootstrap="vendor/autoload.php"
	colors="true"
	displayDetailsOnTestsThatTriggerDeprecations="true"
	displayDetailsOnTestsThatTriggerErrors="true"
	displayDetailsOnTestsThatTriggerNotices="true"
	displayDetailsOnTestsThatTriggerWarnings="true"
	displayDetailsOnPhpunitDeprecations="true"
	processIsolation="false"
	stopOnFailure="false">
	<testsuites>
		<testsuite name="tc-lib-pdf Test Suite">
			<directory>./test</directory>
		</testsuite>
	</testsuites>
	<source>
		<include>
			<directory suffix=".php">src</directory>
		</include>
	</source>
	<coverage>
		<report>
			<clover outputFile="target/coverage/coverage.xml"/>
			<html outputDirectory="target/coverage" lowUpperBound="50" highLowerBound="90"/>
		</report>
	</coverage>
	<logging>
		<junit outputFile="target/logs/junit.xml"/>
	</logging>
</phpunit>
