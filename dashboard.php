<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de Bord - Schluter Systems</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f4f4f4;
            color: #333;
            transition: background-color 0.3s ease, color 0.3s ease;
        }

        header {
            background-color: #333;
            padding: 15px;
            text-align: center;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            z-index: 100;
        }

        header ul {
            list-style: none;
            display: flex;
            justify-content: center;
            width: 100%;
        }

        header ul li {
            margin: 0 20px;
        }

        header ul li a {
            color: white;
            text-decoration: none;
            font-size: 1.1em;
            padding: 10px 20px;
            border-radius: 5px;
            transition: background-color 0.3s ease;
        }

        header ul li a:hover {
            background-color: #f57c00;
        }

        .dashboard {
            margin: 100px auto;
            padding: 20px;
            max-width: 800px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .dashboard-card {
            margin: 20px 0;
            padding: 20px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .dashboard-card h2 {
            color: #f57c00;
        }

        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: 30px;
            width: 100%;
        }

        .footer a {
            color: #f57c00;
            text-decoration: none;
            padding: 0 10px;
        }

        .footer a:hover {
            color: #fff;
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            header ul {
                flex-direction: column;
            }

            header ul li {
                margin: 10px 0;
            }
        }
    </style>
    <script>
        const translations = {
            fr: {
                "title": "Tableau de Bord",
                "stats-title": "Statistiques",
                "visitors": "Nombre de visiteurs :",
                "tickets": "Tickets ouverts :",
                "notifications-title": "Notifications",
                "tasks-title": "Tâches importantes",
                "back-to-top": "Retour en haut"
            },
            en: {
                "title": "Dashboard",
                "stats-title": "Statistics",
                "visitors": "Number of visitors:",
                "tickets": "Open tickets:",
                "notifications-title": "Notifications",
                "tasks-title": "Important Tasks",
                "back-to-top": "Back to Top"
            },
            de: {
                "title": "Dashboard",
                "stats-title": "Statistiken",
                "visitors": "Anzahl der Besucher:",
                "tickets": "Offene Tickets:",
                "notifications-title": "Benachrichtigungen",
                "tasks-title": "Wichtige Aufgaben",
                "back-to-top": "Nach oben"
            }
        };

        function changeLanguage(lang) {
            localStorage.setItem('language', lang);
            document.querySelectorAll('[data-section]').forEach(element => {
                const section = element.getAttribute('data-section');
                if (translations[lang][section]) {
                    element.textContent = translations[lang][section];
                }
            });
        }

        document.addEventListener('DOMContentLoaded', () => {
            const lang = localStorage.getItem('language') || 'fr';
            changeLanguage(lang);
        });

        window.addEventListener('scroll', () => {
            const backToTop = document.getElementById('backToTop');
            backToTop.style.display = window.scrollY > 300 ? 'block' : 'none';
        });

        function scrollToTop() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</head>
<body>
<?php include 'top_bar.php'; ?>

<div class="dashboard">
    <h1 data-section="title">Tableau de Bord</h1>
    <div class="dashboard-card">
        <h2 data-section="stats-title">Statistiques</h2>
        <p data-section="visitors">Nombre de visiteurs :</p>
        <p data-section="tickets">Tickets ouverts :</p>
    </div>
    <div class="dashboard-card">
        <h2 data-section="notifications-title">Notifications</h2>
        <p>Nouvelle mise à jour disponible.</p>
    </div>
    <div class="dashboard-card">
        <h2 data-section="tasks-title">Tâches importantes</h2>
        <ul>
            <li>Préparer la réunion du 27 mars</li>
            <li>Vérifier les tickets ouverts</li>
            <li>Mettre à jour l'inventaire</li>
        </ul>
    </div>
</div>
</body>
<?php include 'bottom_bar.php'; ?>
</html>
