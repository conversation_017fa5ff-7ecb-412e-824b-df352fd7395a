<?php
require_once('config/database.php');

// Fonction pour obtenir la connexion à la base de données
function migrateEndOfLife() {
    global $conn;
    
    // Les fichiers CSV à migrer
    $csvFiles = [
        'pc_installes' => 'pc_installes.csv',
        'pc_prevus' => 'pc_prevus.csv',
        'pc_a_prevoir' => 'pc_a_prevoir.csv'
    ];

    foreach ($csvFiles as $type => $csvFile) {
        if (file_exists($csvFile) && ($handle = fopen($csvFile, 'r')) !== false) {
            // Ignorer l'en-tête
            fgetcsv($handle);
            
            $stmt = $conn->prepare("INSERT INTO changements_poste (nom_utilisateur, ancien_poste, nouveau_poste, date_changement, lifecycle, responsable, type) VALUES (?, ?, ?, ?, ?, ?, ?)");
            
            while (($data = fgetcsv($handle)) !== false) {
                if (count($data) >= 6) {
                    $stmt->execute([
                        $data[0], // nom_utilisateur
                        $data[1], // ancien_poste
                        $data[2], // nouveau_poste
                        $data[3], // date_changement
                        $data[4], // lifecycle
                        $data[5], // responsable
                        $type    // type
                    ]);
                }
            }
            
            fclose($handle);
            // Supprimer le fichier CSV après migration
            unlink($csvFile);
            echo "Migration réussie pour $type<br>";
        } else {
            echo "Fichier $csvFile non trouvé<br>";
        }
    }
}

try {
    migrateEndOfLife();
    echo "Migration des changements de poste terminée avec succès!";
} catch (Exception $e) {
    echo "Erreur lors de la migration : " . $e->getMessage();
}
?>
