<?php
require_once 'config/database.php';
session_start();

// Vérifier si l'utilisateur est connecté et est un administrateur
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    die(json_encode(['error' => 'Non autorisé']));
}

if (isset($_POST['user_id'])) {
    $user_id = intval($_POST['user_id']);
    
    // Générer un mot de passe aléatoire de 8 caractères
    $new_password = substr(str_shuffle('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8);
    
    // Hasher le nouveau mot de passe
    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
    
    try {
        $stmt = $db->prepare("UPDATE users SET password = ? WHERE id = ?");
        $stmt->execute([$hashed_password, $user_id]);
        
        if ($stmt->rowCount() > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Mot de passe réinitialisé avec succès',
                'new_password' => $new_password
            ]);
        } else {
            echo json_encode(['error' => 'Utilisateur non trouvé']);
        }
    } catch (PDOException $e) {
        echo json_encode(['error' => 'Erreur lors de la réinitialisation du mot de passe']);
    }
} else {
    echo json_encode(['error' => 'ID utilisateur non fourni']);
}
