<?php
// Test des nouvelles fonctionnalités PDF et champs optionnels
error_reporting(E_ALL);
ini_set('display_errors', 1);

function getDbConnection() {
    try {
        $conn = new PDO("mysql:host=localhost;dbname=schluter_db;charset=utf8", "root", "");
        $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $conn->exec("SET NAMES utf8");
        return $conn;
    } catch(PDOException $e) {
        die("Erreur de connexion : " . $e->getMessage());
    }
}

function testDatabaseStructure() {
    $conn = getDbConnection();
    
    try {
        // Vérifier la structure de la table
        $sql = "DESCRIBE accuses_reception";
        $stmt = $conn->query($sql);
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $hasPdfColumn = false;
        foreach ($columns as $column) {
            if ($column['Field'] === 'pdf_file_path') {
                $hasPdfColumn = true;
                break;
            }
        }
        
        return [
            'success' => $hasPdfColumn,
            'message' => $hasPdfColumn ? 'Colonne pdf_file_path trouvée' : 'Colonne pdf_file_path manquante',
            'columns' => $columns
        ];
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur : ' . $e->getMessage(),
            'columns' => []
        ];
    }
}

function testOptionalFields() {
    $conn = getDbConnection();
    
    try {
        // Tester l'insertion avec des champs vides
        $sql = "INSERT INTO accuses_reception (collaborateur, responsable, date_creation, materiel_remis, materiel_recu, pdf_file_path) 
                VALUES (:collaborateur, :responsable, :date, :remis, :recu, :pdf_path)";
        
        $stmt = $conn->prepare($sql);
        $result = $stmt->execute([
            ':collaborateur' => 'Test Utilisateur',
            ':responsable' => 'Test Responsable',
            ':date' => date('Y-m-d'),
            ':remis' => null,  // Champ vide
            ':recu' => null,   // Champ vide
            ':pdf_path' => null
        ]);
        
        if ($result) {
            $insertId = $conn->lastInsertId();
            
            // Nettoyer le test
            $deleteSql = "DELETE FROM accuses_reception WHERE id = :id";
            $deleteStmt = $conn->prepare($deleteSql);
            $deleteStmt->execute([':id' => $insertId]);
            
            return [
                'success' => true,
                'message' => 'Test des champs optionnels réussi (ID test: ' . $insertId . ')'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Échec de l\'insertion avec champs vides'
            ];
        }
        
    } catch (PDOException $e) {
        return [
            'success' => false,
            'message' => 'Erreur lors du test : ' . $e->getMessage()
        ];
    }
}

function testUploadDirectory() {
    $uploadDir = 'uploads/pdf/';
    
    $tests = [
        'directory_exists' => is_dir($uploadDir),
        'directory_writable' => is_writable($uploadDir),
        'htaccess_exists' => file_exists($uploadDir . '.htaccess'),
        'index_protection' => file_exists($uploadDir . 'index.php')
    ];
    
    $allPassed = array_reduce($tests, function($carry, $test) {
        return $carry && $test;
    }, true);
    
    return [
        'success' => $allPassed,
        'message' => $allPassed ? 'Dossier d\'upload correctement configuré' : 'Problèmes avec le dossier d\'upload',
        'details' => $tests
    ];
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Fonctionnalités PDF</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background-color: #e8f4fd; border-color: #bee5eb; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; border: 1px solid #ddd; text-align: left; }
        th { background-color: #f8f9fa; }
        .btn { padding: 8px 15px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
    </style>
</head>
<body>
<div class="container">
    <h1>🧪 Test des Nouvelles Fonctionnalités</h1>
    
    <div class="info test-section">
        <h3>📋 Tests Automatiques</h3>
        <p>Cette page teste les modifications apportées au système d'accusés de réception :</p>
        <ul>
            <li>✅ Champs "Remis" et "Reçu" optionnels</li>
            <li>📄 Support des fichiers PDF importés</li>
            <li>🔒 Sécurité du dossier d'upload</li>
            <li>🗄️ Structure de base de données</li>
        </ul>
    </div>
    
    <!-- Test 1: Structure de base de données -->
    <div class="test-section">
        <h3>🗄️ Test 1 : Structure de Base de Données</h3>
        <?php
        $dbTest = testDatabaseStructure();
        $statusClass = $dbTest['success'] ? 'success' : 'error';
        ?>
        <div class="<?= $statusClass ?>">
            <strong>Résultat :</strong> <?= htmlspecialchars($dbTest['message']) ?>
        </div>
        
        <?php if (!empty($dbTest['columns'])): ?>
            <h4>Structure de la table accuses_reception :</h4>
            <table>
                <tr><th>Champ</th><th>Type</th><th>Null</th><th>Clé</th><th>Défaut</th><th>Extra</th></tr>
                <?php foreach ($dbTest['columns'] as $column): ?>
                    <tr>
                        <td><?= htmlspecialchars($column['Field']) ?></td>
                        <td><?= htmlspecialchars($column['Type']) ?></td>
                        <td><?= htmlspecialchars($column['Null']) ?></td>
                        <td><?= htmlspecialchars($column['Key']) ?></td>
                        <td><?= htmlspecialchars($column['Default']) ?></td>
                        <td><?= htmlspecialchars($column['Extra']) ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
    
    <!-- Test 2: Champs optionnels -->
    <div class="test-section">
        <h3>📝 Test 2 : Champs Optionnels</h3>
        <?php
        $optionalTest = testOptionalFields();
        $statusClass = $optionalTest['success'] ? 'success' : 'error';
        ?>
        <div class="<?= $statusClass ?>">
            <strong>Résultat :</strong> <?= htmlspecialchars($optionalTest['message']) ?>
        </div>
        <p><small>Ce test vérifie qu'on peut créer un accusé de réception avec des champs "Remis" et "Reçu" vides.</small></p>
    </div>
    
    <!-- Test 3: Dossier d'upload -->
    <div class="test-section">
        <h3>📁 Test 3 : Dossier d'Upload</h3>
        <?php
        $uploadTest = testUploadDirectory();
        $statusClass = $uploadTest['success'] ? 'success' : 'error';
        ?>
        <div class="<?= $statusClass ?>">
            <strong>Résultat :</strong> <?= htmlspecialchars($uploadTest['message']) ?>
        </div>
        
        <h4>Détails des vérifications :</h4>
        <table>
            <tr><th>Vérification</th><th>Statut</th></tr>
            <tr>
                <td>Dossier uploads/pdf/ existe</td>
                <td class="<?= $uploadTest['details']['directory_exists'] ? 'status-ok' : 'status-error' ?>">
                    <?= $uploadTest['details']['directory_exists'] ? '✅ OK' : '❌ Manquant' ?>
                </td>
            </tr>
            <tr>
                <td>Dossier accessible en écriture</td>
                <td class="<?= $uploadTest['details']['directory_writable'] ? 'status-ok' : 'status-error' ?>">
                    <?= $uploadTest['details']['directory_writable'] ? '✅ OK' : '❌ Non accessible' ?>
                </td>
            </tr>
            <tr>
                <td>Fichier .htaccess présent</td>
                <td class="<?= $uploadTest['details']['htaccess_exists'] ? 'status-ok' : 'status-error' ?>">
                    <?= $uploadTest['details']['htaccess_exists'] ? '✅ OK' : '❌ Manquant' ?>
                </td>
            </tr>
            <tr>
                <td>Protection index.php présente</td>
                <td class="<?= $uploadTest['details']['index_protection'] ? 'status-ok' : 'status-error' ?>">
                    <?= $uploadTest['details']['index_protection'] ? '✅ OK' : '❌ Manquant' ?>
                </td>
            </tr>
        </table>
    </div>
    
    <!-- Actions -->
    <div class="test-section info">
        <h3>🚀 Prochaines Étapes</h3>
        
        <?php if ($dbTest['success'] && $optionalTest['success'] && $uploadTest['success']): ?>
            <div class="success">
                <h4>✅ Tous les tests sont passés !</h4>
                <p>Le système est prêt à utiliser les nouvelles fonctionnalités.</p>
            </div>
            <a href="accuse_reception.php" class="btn btn-success">🔗 Tester l'Interface</a>
        <?php else: ?>
            <div class="error">
                <h4>⚠️ Certains tests ont échoué</h4>
                <p>Veuillez corriger les problèmes avant d'utiliser les nouvelles fonctionnalités.</p>
            </div>
            <?php if (!$dbTest['success']): ?>
                <a href="run_migration.php" class="btn btn-primary">🔧 Exécuter la Migration</a>
            <?php endif; ?>
        <?php endif; ?>
        
        <a href="historique.php" class="btn btn-primary">📋 Voir l'Historique</a>
    </div>
    
    <div class="test-section">
        <h3>📚 Fonctionnalités Disponibles</h3>
        <ul>
            <li><strong>Champs optionnels :</strong> Les champs "Remis" et "Reçu" peuvent être laissés vides</li>
            <li><strong>Import PDF :</strong> Possibilité d'importer des accusés de réception existants en PDF</li>
            <li><strong>Visualisation PDF :</strong> Consultation des PDF importés directement dans le navigateur</li>
            <li><strong>Sécurité :</strong> Validation stricte des fichiers et stockage sécurisé</li>
            <li><strong>UTF-8 :</strong> Support complet des caractères français dans les noms de fichiers</li>
        </ul>
    </div>
</div>
</body>
</html>
